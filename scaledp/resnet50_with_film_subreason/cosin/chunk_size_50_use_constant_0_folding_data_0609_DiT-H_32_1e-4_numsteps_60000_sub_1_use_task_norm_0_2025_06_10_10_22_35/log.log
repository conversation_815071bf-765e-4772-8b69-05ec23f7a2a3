usage: imitate_droid_episodes_ddp.py [-h] [--eval] [--onscreen_render]
                                     --ckpt_dir CKPT_DIR --policy_class
                                     POLICY_CLASS --task_name TASK_NAME
                                     --batch_size BATCH_SIZE --seed SEED
                                     --num_steps NUM_STEPS --lr LR
                                     [--load_pretrain]
                                     [--eval_every EVAL_EVERY]
                                     [--validate_every VALIDATE_EVERY]
                                     [--save_every SAVE_EVERY]
                                     [--skip_mirrored_data]
                                     [--actuator_network_dir ACTUATOR_NETWORK_DIR]
                                     [--history_len HISTORY_LEN]
                                     [--future_len FUTURE_LEN]
                                     [--prediction_len PREDICTION_LEN]
                                     [--kl_weight KL_WEIGHT]
                                     [--chunk_size CHUNK_SIZE]
                                     [--hidden_dim HIDDEN_DIM]
                                     [--dim_feedforward DIM_FEEDFORWARD]
                                     [--temporal_agg] [--use_vq]
                                     [--vq_class VQ_CLASS] [--vq_dim VQ_DIM]
                                     [--no_encoder] [--backbone BACKBONE]
                                     [--no_sepe_backbone] [--use_lang]
                                     [--input_state_acthead]
                                     [--ddp_port DDP_PORT]
                                     [--lr_backbone LR_BACKBONE]
                                     [--pool_class POOL_CLASS]
                                     [--stsm_num_kp STSM_NUM_KP]
                                     [--img_fea_dim IMG_FEA_DIM]
                                     [--cond_obs_dim COND_OBS_DIM]
                                     [--num_noise_samples NUM_NOISE_SAMPLES]
                                     [--use_color_rand]
                                     [--robostate_hidden_dim ROBOSTATE_HIDDEN_DIM]
                                     [--lang_hidden_dim LANG_HIDDEN_DIM]
                                     [--encoder_hidden_dim ENCODER_HIDDEN_DIM]
                                     [--pool_channels POOL_CHANNELS]
                                     [--image_size_w IMAGE_SIZE_W]
                                     [--image_size_h IMAGE_SIZE_H]
                                     [--Resize_images RESIZE_IMAGES]
                                     [--trans_hidden_dim TRANS_HIDDEN_DIM]
                                     [--lang_raw LANG_RAW]
                                     [--pretrain_data PRETRAIN_DATA]
                                     [--batch_size_val BATCH_SIZE_VAL]
                                     [--bgr BGR] [--model_size MODEL_SIZE]
                                     [--is_use_sub_reason IS_USE_SUB_REASON]
                                     [--use_filmfusion USE_FILMFUSION]
                                     [--use_base USE_BASE] [--resume RESUME]
                                     [--load_pre_path LOAD_PRE_PATH]
                                     [--use_resnet_film USE_RESNET_FILM]
                                     [--use_moe USE_MOE]
                                     [--use_task_norm USE_TASK_NORM]
                                     [--not_use_soft_router NOT_USE_SOFT_ROUTER]
                                     [--use_constant USE_CONSTANT]
                                     [--use_inspair_hand USE_INSPAIR_HAND]
                                     [--only_use_base ONLY_USE_BASE]
                                     [--ema_update_frequency EMA_UPDATE_FREQUENCY]
                                     [--ema_update_after_step EMA_UPDATE_AFTER_STEP]
                                     [--action_dim ACTION_DIM]
                                     [--state_dim STATE_DIM]
imitate_droid_episodes_ddp.py: error: unrecognized arguments: --double_arm 1

