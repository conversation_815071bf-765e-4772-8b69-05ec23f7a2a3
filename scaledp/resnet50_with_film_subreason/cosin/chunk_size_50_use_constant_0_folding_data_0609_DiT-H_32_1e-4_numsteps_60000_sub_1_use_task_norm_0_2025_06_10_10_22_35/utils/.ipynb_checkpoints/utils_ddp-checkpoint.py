import torch
import os
import h5py
import pickle
import fnmatch
import cv2
import pickle
from torch.utils.data import  DataLoader
import torchvision.transforms as transforms
from torch.utils.data import  DistributedSampler
import json

import numpy as np


def flatten_list(l):
    return [item for sublist in l for item in sublist]

class EpisodicDataset(torch.utils.data.Dataset):
    def __init__(self, dataset_path_list, camera_names, norm_stats, episode_ids, episode_len, chunk_size, 
                   args=None, dataset_dir_l=None,rank=None):
        super(EpisodicDataset).__init__()
        self.episode_ids = episode_ids
        self.dataset_path_list = dataset_path_list
        self.camera_names = camera_names
        self.norm_stats = norm_stats
        self.episode_len = episode_len
        self.chunk_size = chunk_size

        self.bgr = args['bgr']
        self.is_use_sub_reason = args['is_use_sub_reason']
        self.cumulative_len = np.cumsum(self.episode_len)
        self.use_base = args['use_base']
        self.args=args
        self.dataset_dir_l = dataset_dir_l
        if self.is_use_sub_reason:
            for dataset_dir in self.dataset_dir_l:
                with open(os.path.join(dataset_dir, "reason.json"), "r") as f:
                    sub_dict = json.load(f)
                self.sub_dict.update(sub_dict)
            for dataset_dir in self.dataset_dir_l:
                with open(os.path.join(dataset_dir, "reason.pkl"), "rb") as f:
                    sub_data_encoder = pickle.load(f)
                self.sub_data_encoder.update(sub_data_encoder)
        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>load distilbert dict done>>>>>>>>>>>>>>>>>>")
        if rank is None and rank == 0:
            print(f"total dataset cumulative_len: {self.cumulative_len}")
        else:
            print(f"total dataset cumulative_len: {self.cumulative_len}")
        self.max_episode_len = max(episode_len)

        # augment images for training (default for dp and scaledp)
        self.augment_images = True

        self.resize_shape = (args["image_size_h"], args["image_size_w"])
        if rank is None and rank == 0:
            print(f"cur dataset augment_images: {self.augment_images}")
            print(f"cur image resize_shape: {self.resize_shape}")
        else:
            print(f"cur dataset augment_images: {self.augment_images}")
            print(f"cur image resize_shape: {self.resize_shape}")
        self.transformations = None
        self.transformations_image = [
            transforms.Resize(self.resize_shape, antialias=True),
        ]
        self.lang_embed = torch.zeros(1)
        self.pre = args["pretrain_data"]
        if self.args['use_task_norm'] :
            print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>use task norm>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        self.__getitem__(0)

    def __len__(self):
        return 1000 * sum(self.episode_len)

    def _locate_transition(self, index):
        assert index < self.cumulative_len[-1]
        episode_index = np.argmax(self.cumulative_len > index)  # argmax returns first True index
        start_ts = index - (self.cumulative_len[episode_index] - self.episode_len[episode_index])
        episode_id = self.episode_ids[episode_index]
        return episode_id, start_ts

    def __getitem__(self, index):
        index = index % sum(self.episode_len)  # Ensure index wraps around
        episode_id, start_ts = self._locate_transition(index)
        dataset_path = self.dataset_path_list[episode_id]
        with h5py.File(dataset_path, 'r') as root:
            lang_embed = None
            if self.is_use_sub_reason:
                try:
                    sub = self.sub_dict[dataset_path]
                    if type(sub) == list:
                        lang_embed = self.sub_data_encoder[sub[start_ts]]
                        del sub
                    else:
                        lang_embed = self.sub_data_encoder[sub]
                        del sub
                    lang_embed = torch.from_numpy(lang_embed).float().squeeze()
                except Exception as e:
                    print(dataset_path)
                    print(e)
                    exit(0)
                # print(lang_embed.shape)
            elif 'language_distilbert' in root:
                lang_embed = root['language_distilbert'][:]
                lang_embed = torch.from_numpy(lang_embed).float().squeeze()
            else:
                print(">>>>>>>>>>>>>>>>No language_distilbert in hdf5 file>>>>>>>>>>>>>>>>>>>")
                lang_embed = self.lang_embed
            compressed = root.attrs.get('compress', False)

            qpos = root['/observations/qpos'][start_ts]
            if self.args["use_base"]:
                if self.args["only_use_base"]:
                    base_action = root['/base_action'][()]
                    base_action = preprocess_base_action(base_action)
                    action = base_action
                    qpos = base_action[start_ts]
                elif '/base_action' in root:
                    base_action = root['/base_action'][()]
                    base_action = preprocess_base_action(base_action)
                    action = np.concatenate([root['/action'][()], base_action], axis=-1)
                    qpos = np.concatenate([qpos, base_action[start_ts]], axis=-1)
                else:
                    base_action = np.zeros((root['/action'][()].shape[0], 2))
                    base_action = preprocess_base_action(base_action)
                    action = np.concatenate([root['/action'][()], base_action], axis=-1)
                    qpos = np.concatenate([qpos, base_action[start_ts]], axis=-1)
            else:
                action = root['/action'][()][:]
            original_action_shape = action.shape
            episode_len = original_action_shape[0]
            image_dict = dict()
            for cam_name in self.camera_names:
                image_dict[cam_name] = root[f'/observations/images/{cam_name}'][start_ts]


            if compressed:
                for cam_name in image_dict.keys():
                    decompressed_image = cv2.imdecode(image_dict[cam_name], 1)
                    image_dict[cam_name] = np.array(decompressed_image)
                    image_dict[cam_name] = cv2.resize(image_dict[cam_name],
                                                  (self.resize_shape[1], self.resize_shape[0]))
            else:
                image_dict[cam_name] = cv2.resize(image_dict[cam_name],
                                                  (self.resize_shape[1], self.resize_shape[0]))
            action = action[max(0, start_ts - 1):]  # hack, to make timesteps more aligned
            action_len = episode_len - max(0, start_ts - 1)  # hack, to make timesteps more aligned


        root.close()
        padded_action = np.zeros((self.max_episode_len, original_action_shape[1]), dtype=np.float32)
        padded_action[:action_len] = action
        is_pad = np.zeros(self.max_episode_len)
        is_pad[action_len:] = 1

        padded_action = padded_action[:self.chunk_size]
        is_pad = is_pad[:self.chunk_size]

        # new axis for different cameras
        all_cam_images = []
        for cam_name in self.camera_names:
            if self.bgr:
                all_cam_images.append(image_dict[cam_name][:, :, ::-1])
            else:
                all_cam_images.append(image_dict[cam_name])
        all_cam_images = np.stack(all_cam_images, axis=0)

        # construct observations
        image_data = torch.from_numpy(all_cam_images)
        qpos_data = torch.from_numpy(qpos).float()
        action_data = torch.from_numpy(padded_action).float()
        is_pad = torch.from_numpy(is_pad).bool()

        # channel last
        image_data = torch.einsum('k h w c -> k c h w', image_data)

        del image_dict
        del padded_action
        del original_action_shape
        # augmentation
        if self.transformations is None:
            print('Initializing transformations')
            original_size = image_data.shape[2:]
            ratio = 0.95
            self.transformations = [
                transforms.RandomCrop(size=[int(original_size[0] * ratio), int(original_size[1] * ratio)]),
                transforms.Resize(original_size, antialias=True),
                transforms.RandomRotation(degrees=[-5.0, 5.0], expand=False),
                transforms.ColorJitter(brightness=0.3, contrast=0.4, saturation=0.5)  # , hue=0.08)
            ]

        if self.augment_images:
            for transform in self.transformations:
                image_data = transform(image_data)
        # normalize image and change dtype to float
        image_data = image_data / 255.0

        norm_stats = self.norm_stats

        action_data = ((action_data - norm_stats["action_min"]) / (
                    norm_stats["action_max"] - norm_stats["action_min"])) * 2 - 1

        qpos_data = (qpos_data - norm_stats["qpos_mean"]) / norm_stats["qpos_std"]

        return image_data, qpos_data, action_data, is_pad, lang_embed,  dataset_path, episode_id


class DistributedBatchSampler(torch.utils.data.Sampler):
    def __init__(self, dataset, batch_size, episode_len_l, sample_weights, num_replicas=None, rank=None, shuffle=True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.episode_len_l = episode_len_l
        self.sample_weights = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
        self.num_replicas = num_replicas
        self.rank = rank
        self.shuffle = shuffle

        self.sample_probs = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
        self.sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in episode_len_l])

        self.dist_sampler = DistributedSampler(self.dataset, num_replicas=self.num_replicas, rank=self.rank,
                                               shuffle=self.shuffle)

    """
    def __iter__(self):
        sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in self.episode_len_l])
        episode_indices = np.arange(len(self.episode_len_l))
        for _ in self.dist_sampler:
            batch = []
            attempts = 0
            while len(batch) < self.batch_size:
                episode_idx = np.random.choice(episode_indices, p=self.sample_weights)
                if self.episode_len_l[episode_idx] > 0:  # Ensure the episode has data
                    step_idx = np.random.randint(sum_dataset_len_l[episode_idx], sum_dataset_len_l[episode_idx + 1])
                    batch.append(step_idx)
                attempts += 1
                if attempts > 100 * self.batch_size:  # Safeguard against infinite loop
                    raise RuntimeError("Unable to generate a valid batch after many attempts")
            yield batch
    """

    def __iter__(self):
        batch = []
        for idx in self.dist_sampler:
            # Assume episode index selection and within-episode index calculation is done here
            # For simplicity, we're just incrementing idx to mimic selecting indices
            batch.append(idx)
            if len(batch) == self.batch_size:
                yield batch
                batch = []
        if batch:
            yield batch  # Yield the last batch if it's not empty

    def __len__(self):
        return len(self.dist_sampler) // self.batch_size


def get_norm_stats_by_tasks(dataset_path_list,args):
    data_tasks_dict = dict(
        fold_shirt=[],
        clean_table=[],
        others=[],
    )
    for dataset_path in dataset_path_list:
        if 'fold' in dataset_path or 'shirt' in dataset_path:
            key = 'fold_shirt'
        elif 'clean_table' in dataset_path and 'pick' not in dataset_path:
            key = 'clean_table'
        else:
            key = 'others'
            base_action = preprocess_base_action(base_action)
        data_tasks_dict[key].append(dataset_path)
    norm_stats_tasks = {k: None for k in data_tasks_dict.keys()}
    for k, v in data_tasks_dict.items():
        if len(v) > 0:
            norm_stats_tasks[k], _ = get_norm_stats(v,args=args)
    return norm_stats_tasks



def get_norm_stats(dataset_path_list,args):
    all_qpos_data = []
    all_action_data = []
    all_episode_len = []

    for dataset_path in dataset_path_list:
        try:
            with h5py.File(dataset_path, 'r') as root:
                qpos = root['/observations/qpos'][()]

                if args["use_base"]:
                    if args["only_use_base"]:
                        base_action = root['/base_action'][()]
                        base_action = preprocess_base_action(base_action)
                        action = base_action
                        qpos = base_action
                    elif '/base_action' in root:
                        base_action = root['/base_action'][()]
                        type_flag_index = root['type_flag']
                        action = np.concatenate([root['/action'][()], base_action], axis=-1)
                        qpos = np.concatenate([qpos, base_action], axis=-1)
                    else:
                        base_action = np.zeros((qpos.shape[0], 2))
                        base_action = preprocess_base_action(base_action)
                        action = np.concatenate([root['/action'][()], base_action], axis=-1)
                        qpos = np.concatenate([qpos, base_action], axis=-1)
                else:
                    action = root['/action'][()][:]

        except Exception as e:
            print(f'Error loading {dataset_path} in get_norm_stats')
            print(e)
            quit()
        all_qpos_data.append(torch.from_numpy(qpos))
        all_action_data.append(torch.from_numpy(action))
        all_episode_len.append(len(qpos))

    all_qpos_data = torch.cat(all_qpos_data, dim=0)
    all_action_data = torch.cat(all_action_data, dim=0)

    # normalize action data
    action_mean = all_action_data.mean(dim=[0]).float()
    action_std = all_action_data.std(dim=[0]).float()
    action_std = torch.clip(action_std, 1e-2, np.inf)  # clipping

    # normalize qpos data
    qpos_mean = all_qpos_data.mean(dim=[0]).float()
    qpos_std = all_qpos_data.std(dim=[0]).float()
    qpos_std = torch.clip(qpos_std, 1e-2, np.inf)  # clipping

    action_min = all_action_data.min(dim=0).values.float()
    action_max = all_action_data.max(dim=0).values.float()
    del all_action_data, all_qpos_data
    eps = 0.0001
    stats = {"action_mean": action_mean.numpy(), "action_std": action_std.numpy(),
             "action_min": action_min.numpy() - eps, "action_max": action_max.numpy() + eps,
             "qpos_mean": qpos_mean.numpy(), "qpos_std": qpos_std.numpy(),
             "example_qpos": qpos}

    return stats, all_episode_len


def find_all_hdf5(dataset_dir):
    hdf5_files = []
    for root, dirs, files in os.walk(dataset_dir):

        for filename in fnmatch.filter(files, '*.hdf5'):
            if 'features' in filename: continue
            if 'pointclouds' in filename: continue
            if "without_pull_back" in os.path.join(root,filename): continue
            hdf5_files.append(os.path.join(root, filename))
    print(f'Found {len(hdf5_files)} hdf5 files')
    if len(hdf5_files) == 0:
        print(f'No hdf5 files found in {dataset_dir}')
        exit(0)
    return hdf5_files


def BatchSampler(batch_size, episode_len_l, sample_weights):
    sample_probs = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
    sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in episode_len_l])
    while True:
        batch = []
        for _ in range(batch_size):
            episode_idx = np.random.choice(len(episode_len_l), p=sample_probs)
            step_idx = np.random.randint(sum_dataset_len_l[episode_idx], sum_dataset_len_l[episode_idx + 1])
            batch.append(step_idx)
        yield batch


#


def load_data(dataset_dir_l, camera_names, batch_size_train, chunk_size,
              skip_mirrored_data=False, stats_dir_l=None, sample_weights=None,
              args=None, rank=None):
    if type(dataset_dir_l) == str:
        dataset_dir_l = [dataset_dir_l]
    dataset_path_list_list = [find_all_hdf5(dataset_dir) for dataset_dir in dataset_dir_l]
    dataset_path_list = flatten_list(dataset_path_list_list)
    num_episodes_l = [len(dataset_path_list) for dataset_path_list in dataset_path_list_list]  # get the number of episode
    num_episodes_cumsum = [0] + np.cumsum(num_episodes_l).tolist()

    train_episode_ids_l = [np.arange(num_episodes) + num_episodes_cumsum[idx] for idx, num_episodes in enumerate(num_episodes_l[0:])]
    train_episode_ids = np.concatenate(train_episode_ids_l)
    if rank is not None and rank == 0:
        print(f'Data from: {dataset_dir_l}\n- Train on {[len(x) for x in train_episode_ids_l]} episodes')
    else:
        print(f'Data from: {dataset_dir_l}\n- Train on {[len(x) for x in train_episode_ids_l]} episodes')
    with open(os.path.join(args["ckpt_dir"], "dataset_stats.pkl"), "rb") as f:
        norm_stats = pickle.load(f)
    all_episode_len = norm_stats["all_episode_len"]

    train_episode_len_l = [[all_episode_len[i] for i in train_episode_ids] for train_episode_ids in train_episode_ids_l]
    train_episode_len = flatten_list(train_episode_len_l)

    if rank is not None and rank == 0:
        print(f'Norm stats from: {stats_dir_l}')
        print(f'train_episode_len_l: {train_episode_len_l}')
    else:
        print(f'Norm stats from: {stats_dir_l}')
        print(f'train_episode_len_l: {train_episode_len_l}')

    if torch.cuda.device_count == 1:
        batch_sampler_train = BatchSampler(batch_size_train, train_episode_len_l, sample_weights)
        # construct dataset and dataloader
        train_dataset = EpisodicDataset(dataset_path_list, camera_names, norm_stats, train_episode_ids,
                                        train_episode_len, chunk_size,  args=args, rank=rank)
        train_num_workers = 16  # 16
        # pin_memory=True
        train_dataloader = DataLoader(train_dataset, batch_sampler=batch_sampler_train, pin_memory=True,
                                      num_workers=train_num_workers, prefetch_factor=2)
    else:
        train_dataset = EpisodicDataset(dataset_path_list, camera_names, norm_stats, train_episode_ids,
                                        train_episode_len, chunk_size, args=args,dataset_dir_l=dataset_dir_l)
        num_replicas = torch.cuda.device_count()
        train_num_workers = 8 # 2
        batch_sampler_train = DistributedBatchSampler(train_dataset, batch_size_train, train_episode_len,
                                                      sample_weights, num_replicas=num_replicas, rank=rank)
        # prefetch_factor=2
        train_dataloader = DataLoader(
            train_dataset,
            batch_sampler=batch_sampler_train, pin_memory=True, num_workers=train_num_workers)

    return train_dataloader, norm_stats

def calibrate_linear_vel(base_action, c=None):
    if c is None:
        c = 0.0  # 0.19
    v = base_action[..., 0]
    w = base_action[..., 1]
    base_action = base_action.copy()
    base_action[..., 0] = v - c * w
    return base_action


def smooth_base_action(base_action):
    return np.stack([
        np.convolve(base_action[:, i], np.ones(5) / 5, mode='same') for i in range(base_action.shape[1])
    ], axis=-1).astype(np.float32)


def preprocess_base_action(base_action):

    base_action = smooth_base_action(base_action)

    return base_action


def postprocess_base_action(base_action):
    linear_vel, angular_vel = base_action
    linear_vel *= 1.0
    angular_vel *= 1.0
    # angular_vel = 0

    return np.array([linear_vel, angular_vel])


### env utils






### helper functions







def set_seed(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)
