import os
from aloha_scripts.constants import TASK_CONFIGS
import h5py

steps=0
ep=0
folding_steps=0
for task in TASK_CONFIGS["3_cameras_all_data_1_19"]['dataset_dir']:
    h5_list = os.listdir(task)
    for h5_file in h5_list:
        print(os.path.join(task, h5_file))
        if h5_file.endswith(".hdf5"):
            ep+=1
            with h5py.File(os.path.join(task, h5_file), 'r') as f:
                print(f.keys)
                steps+=f["action"].shape[0]
                if "folding" in task:
                    folding_steps+=f["action"].shape[0]




print(f"fold_t_shirt_easy_version_all all steps",steps)
print(" fold_t_shirt_easy_version_all all episodes",ep)
print("floding steps",folding_steps)