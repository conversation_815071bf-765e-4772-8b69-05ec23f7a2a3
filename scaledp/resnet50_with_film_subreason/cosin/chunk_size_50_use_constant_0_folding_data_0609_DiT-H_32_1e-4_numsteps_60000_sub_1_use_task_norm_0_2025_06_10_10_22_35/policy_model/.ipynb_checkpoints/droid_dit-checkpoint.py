import warnings
import math
import random
import logging
import numpy as np

from typing import Tuple

try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

import torch
import torch.nn as nn
import torch.utils.checkpoint

import torchvision.transforms as transforms
import torchvision.transforms.functional as TVF

from torch.jit import Final
from torch.nn import functional as F
from torchvision.transforms import <PERSON><PERSON>, Compose

from timm.models.vision_transformer import Mlp, use_fused_attn
from utils.fusion_modules import FiLM

#### init backbone ####
from vision_net.backbone import build_backbone
from vision_net.film_backbon import build_film_backbone

from vision_net.robomimic_basenet import SpatialSoftmax
from vision_net.robomimic_diffusion_net import replace_bn_with_gn

from diffusers.schedulers.scheduling_ddim import DDIMScheduler
from diffusers.training_utils import EMAModel

class ColorRandomizer(nn.Module):
    """
    Randomly sample color jitter at input, and then average across color jtters at output.
    """

    def __init__(
            self,
            input_shape,
            brightness=0.3,
            contrast=0.3,
            saturation=0.3,
            hue=0.3,
            num_samples=1,
    ):
        """
        Args:
            input_shape (tuple, list): shape of input (not including batch dimension)
            brightness (None or float or 2-tuple): How much to jitter brightness. brightness_factor is chosen uniformly
                from [max(0, 1 - brightness), 1 + brightness] or the given [min, max]. Should be non negative numbers.
            contrast (None or float or 2-tuple): How much to jitter contrast. contrast_factor is chosen uniformly
                from [max(0, 1 - contrast), 1 + contrast] or the given [min, max]. Should be non negative numbers.
            saturation (None or float or 2-tuple): How much to jitter saturation. saturation_factor is chosen uniformly
                from [max(0, 1 - saturation), 1 + saturation] or the given [min, max]. Should be non negative numbers.
            hue (None or float or 2-tuple): How much to jitter hue. hue_factor is chosen uniformly from [-hue, hue] or
                the given [min, max]. Should have 0<= hue <= 0.5 or -0.5 <= min <= max <= 0.5. To jitter hue, the pixel
                values of the input image has to be non-negative for conversion to HSV space; thus it does not work
                if you normalize your image to an interval with negative values, or use an interpolation that
                generates negative values before using this function.
            num_samples (int): number of random color jitters to take
        """
        super(ColorRandomizer, self).__init__()

        assert len(input_shape) == 3  # (C, H, W)

        self.input_shape = input_shape
        self.brightness = [max(0, 1 - brightness), 1 + brightness] if type(brightness) in {float, int} else brightness
        self.contrast = [max(0, 1 - contrast), 1 + contrast] if type(contrast) in {float, int} else contrast
        self.saturation = [max(0, 1 - saturation), 1 + saturation] if type(saturation) in {float, int} else saturation
        self.hue = [-hue, hue] if type(hue) in {float, int} else hue
        self.num_samples = num_samples

    @torch.jit.unused
    def get_transform(self):
        """
        Get a randomized transform to be applied on image.

        Implementation taken directly from:

        https://github.com/pytorch/vision/blob/2f40a483d73018ae6e1488a484c5927f2b309969/torchvision/transforms/transforms.py#L1053-L1085

        Returns:
            Transform: Transform which randomly adjusts brightness, contrast and
            saturation in a random order.
        """
        transforms = []

        if self.brightness is not None:
            brightness_factor = random.uniform(self.brightness[0], self.brightness[1])
            transforms.append(Lambda(lambda img: TVF.adjust_brightness(img, brightness_factor)))

        if self.contrast is not None:
            contrast_factor = random.uniform(self.contrast[0], self.contrast[1])
            transforms.append(Lambda(lambda img: TVF.adjust_contrast(img, contrast_factor)))

        if self.saturation is not None:
            saturation_factor = random.uniform(self.saturation[0], self.saturation[1])
            transforms.append(Lambda(lambda img: TVF.adjust_saturation(img, saturation_factor)))

        if self.hue is not None:
            hue_factor = random.uniform(self.hue[0], self.hue[1])
            transforms.append(Lambda(lambda img: TVF.adjust_hue(img, hue_factor)))

        random.shuffle(transforms)
        transform = Compose(transforms)

        return transform

    def get_batch_transform(self, N):
        """
        Generates a batch transform, where each set of sample(s) along the batch (first) dimension will have the same
        @N unique ColorJitter transforms applied.

        Args:
            N (int): Number of ColorJitter transforms to apply per set of sample(s) along the batch (first) dimension

        Returns:
            Lambda: Aggregated transform which will autoamtically apply a different ColorJitter transforms to
                each sub-set of samples along batch dimension, assumed to be the FIRST dimension in the inputted tensor
                Note: This function will MULTIPLY the first dimension by N
        """
        return Lambda(lambda x: torch.stack([self.get_transform()(x_) for x_ in x for _ in range(N)]))

    def output_shape_in(self, input_shape=None):
        # outputs are same shape as inputs
        return list(input_shape)

    def output_shape_out(self, input_shape=None):
        # since the forward_out operation splits [B * N, ...] -> [B, N, ...]
        # and then pools to result in [B, ...], only the batch dimension changes,
        # and so the other dimensions retain their shape.
        return list(input_shape)

    def _forward_in(self, inputs):
        """
        Samples N random color jitters for each input in the batch, and then reshapes
        inputs to [B * N, ...].
        """
        assert len(inputs.shape) >= 3  # must have at least (C, H, W) dimensions

        # Make sure shape is exactly 4
        if len(inputs.shape) == 3:
            inputs = torch.unsqueeze(inputs, dim=0)

        # TODO: Make more efficient other than implicit for-loop?
        # Create lambda to aggregate all color randomizings at once
        transform = self.get_batch_transform(N=self.num_samples)

        return transform(inputs)

    def __repr__(self):
        """Pretty print network."""
        header = '{}'.format(str(self.__class__.__name__))
        msg = header + f"(input_shape={self.input_shape}, brightness={self.brightness}, contrast={self.contrast}, " \
                       f"saturation={self.saturation}, hue={self.hue}, num_samples={self.num_samples})"
        return msg


class DroidDiTPolicy(nn.Module):
    def __init__(self, args_override, rank=None):
        super().__init__()
        self.camera_names = args_override['camera_names']

        self.observation_horizon = args_override['observation_horizon']  ### TODO TODO TODO DO THIS
        self.prediction_horizon = args_override['prediction_horizon']  # chunk size
        # self.num_inference_timesteps = args_override['num_inference_timesteps']

        self.num_inference_timesteps = 10  # 30 #15 #30 #10 #15 #30 #50 #100 #16 #args_override['num_inference_timesteps']

        self.ema_power = args_override['ema_power']  # 0.75
        self.lr = args_override['lr']  # 1e-4
        self.weight_decay = args_override['weight_decay']  # 0.0 # 1e-6

        self.pool_class = args_override['pool_class']  # null

        self.stsm_num_kp = args_override['stsm_num_kp']  # 512

        self.img_fea_dim = args_override['img_fea_dim']  # 512
        self.ac_dim = args_override['action_dim']  # 14 + 2
        self.state_dim = args_override['state_dim']  # 14 + 2

        self.num_queries = args_override['num_queries']
        self.num_noise_samples = args_override['num_noise_samples']  # 8
        self.use_resnet_film = args_override['use_resnet_film']
        self.ema_update_frequency = args_override['ema_update_frequency']
        print("num_noise_samples: ", self.num_noise_samples)

        print(args_override)

        # have aug data in dataset!
        self.use_color_rand = False
        if self.use_color_rand:
            self.color_randomizer = ColorRandomizer(input_shape=[3, 270, 480])
        else:
            self.color_randomizer = None


        import argparse
        if "clip" in args_override['backbone']:
            self.norm = False
        else:
            self.norm = True
        print("backbone name !!", args_override['backbone'], "film" in args_override['backbone'])
        self.use_film = "film" in args_override['backbone']
        self.use_filmfusion = args_override['use_filmfusion']
        print("FiLM: ", self.use_film)
        self.use_lang = args_override['use_lang']
        self.backbone_name = args_override['backbone']
        self.lr_backbone = args_override['lr_backbone']
        self.image_size_raw_h = int(args_override['image_size_h'])
        self.image_size_raw_w = int(args_override['image_size_w'])
        self.image_size_h = int(args_override['image_size_h']) / 32  # type=int,default=480)
        self.image_size_w = int(args_override['image_size_w']) / 32,  # type=int,default=640)
        self.image_size_h = int(np.ceil(self.image_size_h))
        self.image_size_w = int(np.ceil(self.image_size_w))

        backbones = []
        pools = []
        linears = []
        for _ in self.camera_names:

            backbone = build_backbone(args_override)
            backbones.append(backbone)

            if self.pool_class == 'SpatialSoftmax':
                if self.backbone_name == 'resnet50':
                    # [2048, 9, 15] for 270 480 droid, resnet 50
                    input_shape = [2048, self.image_size_h, self.image_size_w]  # 640//32 480//32
                pools.append(
                    nn.Sequential(
                        SpatialSoftmax(**{'input_shape': input_shape, 'num_kp': self.stsm_num_kp, 'temperature': 1.0,
                                          'learnable_temperature': False, 'noise_std': 0.0}),
                        nn.Flatten(start_dim=1, end_dim=-1)
                    )
                )
                linears.append(
                    nn.Sequential(
                        nn.Linear(int(np.prod([self.stsm_num_kp, 2])), self.stsm_num_kp),
                        nn.ReLU(),
                        nn.Linear(self.stsm_num_kp, self.img_fea_dim)
                    )
                )

        backbones = nn.ModuleList(backbones)
        pools = nn.ModuleList(pools)
        linears = nn.ModuleList(linears)

        if self.use_filmfusion:
            self.obs_input_dim_film = self.img_fea_dim * len(self.camera_names)
            self.film_fusion = FiLM(self.obs_input_dim_film, 768)
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim
        else:
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim

        self.cond_obs_dim = args_override['cond_obs_dim']  # 512
        self.combine = nn.Sequential(
            nn.Linear(self.obs_input_dim, 1024),
            nn.ReLU(),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, self.cond_obs_dim)
        )
        if "resnet" in self.backbone_name:
            backbones = replace_bn_with_gn(backbones)  # TODO
        print(args_override)
        print(f'Build noise_pred_net {args_override["model_size"]}!')
        print(f"noise_pred_net {args_override['model_size']} cond obs dim: {self.cond_obs_dim}")

        model_size = args_override['model_size']
        noise_pred_net = DiT_models[model_size](
            input_dim = self.ac_dim,
            output_dim = self.ac_dim,
            cond_dim = self.cond_obs_dim,
            horizon = self.prediction_horizon,
        )
        if self.use_filmfusion:
            self.nets = nn.ModuleDict({
                'policy': nn.ModuleDict({
                    'backbones': backbones,
                    'pools': pools,
                    'linears': linears,
                    'combine': self.combine,
                    'film_fusion': self.film_fusion,
                    'noise_pred_net': noise_pred_net
                })
            })
        else:
            self.nets = nn.ModuleDict({
                'policy': nn.ModuleDict({
                    'backbones': backbones,
                    'pools': pools,
                    'linears': linears,
                    'combine': self.combine,
                    'noise_pred_net': noise_pred_net
                })
            })

        ENABLE_EMA = True
        if ENABLE_EMA:
            ema = EMAModel(model=self.nets.cuda(), power=self.ema_power,update_after_step=args_override['ema_update_after_step'])  # .to(device=rank)

        else:
            ema = None
        self.ema = ema

        self.noise_scheduler = DDIMScheduler(
            num_train_timesteps=100,
            beta_schedule='squaredcos_cap_v2',
            clip_sample=True,
            set_alpha_to_one=True,
            steps_offset=0,
            prediction_type='epsilon'
        )

        n_parameters = sum(p.numel() for p in self.parameters())

        print(f"droid_dit-{model_size} number of parameters: %.2fM" % (n_parameters / 1e6,))
        total_params, trainable_params = noise_pred_net.count_parameters()
        print(f"noise_pred_net number of parameters: %.2fM" % (total_params / 1e6,))
        print(f"noise_pred_net trainable parameters: %.2fM" % (trainable_params / 1e6,))

    def configure_optimizers(self):
        param_dicts = [
            {"params": [p for n, p in self.nets.named_parameters() if "backbone" not in n and p.requires_grad]},
            {
                "params": [p for n, p in self.nets.named_parameters() if "backbone" in n and p.requires_grad],
                "lr": self.lr_backbone,
            },
        ]
        optimizer = torch.optim.AdamW(param_dicts, lr=self.lr,
                                      weight_decay=self.weight_decay)

        return optimizer

    def __call__(self, qpos, image, actions=None, is_pad=None, language_distilbert=None,step=1):
        lang_embed = language_distilbert
        B = qpos.shape[0]
        normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                         std=[0.229, 0.224, 0.225])

        if self.norm:
            image = normalize(image)
        if actions is not None:  # training time

            actions = actions[:, :self.num_queries, :self.ac_dim]
            is_pad = is_pad[:, :self.num_queries]

            all_cam_features = []

            for cam_id, cam_name in enumerate(self.camera_names):
                cur_img = image[:, cam_id]
                if self.color_randomizer is not None:
                    cur_img = self.color_randomizer._forward_in(cur_img)
                features= self.nets['policy']['backbones'][cam_id](cur_img)

                pool_features = self.nets['policy']['pools'][cam_id](features)
                out_features = self.nets['policy']['linears'][cam_id](pool_features)

                all_cam_features.append(out_features)

            if self.use_filmfusion:
                all_cam_features = torch.cat(all_cam_features, dim=1)
                identity = all_cam_features
                all_cam_features = self.nets['policy']['film_fusion'](all_cam_features, lang_embed)
                obs_cond = all_cam_features + identity
                obs_cond = torch.cat([obs_cond, qpos], dim=1)
            else:
                obs_cond = torch.cat(all_cam_features + [qpos], dim=1)
            obs_cond = self.nets['policy']['combine'](obs_cond) # 2. obs_cond size: torch.Size([64, 512])

            # sample noise to add to actions

            noise = torch.randn([self.num_noise_samples] + list(actions.shape), device=obs_cond.device)

            # sample a diffusion iteration for each data point
            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps,
                (B,), device=obs_cond.device
            ).long()

            # add noise to the clean actions according to the noise magnitude at each diffusion iteration
            timesteps, noise = timesteps.to(obs_cond.device), noise.to(obs_cond.device)

            noisy_actions = torch.cat([self.noise_scheduler.add_noise(
                actions, noise[i], timesteps)
                for i in range(len(noise))], dim=0)  # noisy_actions size: torch.Size([512, 50, 10])

            obs_cond = obs_cond.repeat(self.num_noise_samples, 1)  # obs_cond size: torch.Size([512, 1031])
            timesteps = timesteps.repeat(self.num_noise_samples)  # timesteps size: torch.Size([512])
            is_pad = is_pad.repeat(self.num_noise_samples, 1)


            # predict the noise residual
            noise_pred = self.nets['policy']['noise_pred_net'](noisy_actions, timesteps, global_cond=obs_cond)

            # L2 loss
            noise = noise.view(noise.size(0) * noise.size(1), *noise.size()[2:])

            all_l2 = F.mse_loss(noise_pred, noise, reduction='none')

            loss = (all_l2 * ~is_pad.unsqueeze(-1)).mean()

            loss_dict = {}
            loss_dict['l2_loss'] = loss
            loss_dict['loss'] = loss

            if self.training and self.ema is not None:
                if step % self.ema_update_frequency==0:
                    self.ema.step(self.nets)
            return loss_dict
        else:  # inference time
            To = self.observation_horizon
            Tp = self.prediction_horizon
            action_dim = self.ac_dim

            nets = self.nets
            if self.ema is not None:
                nets = self.ema.averaged_model

            all_cam_features = []
            for cam_id, cam_name in enumerate(self.camera_names):

                features = nets['policy']['backbones'][cam_id](image[:, cam_id])

                pool_features = nets['policy']['pools'][cam_id](features)

                out_features = nets['policy']['linears'][cam_id](pool_features)
                all_cam_features.append(out_features)

            if self.use_filmfusion:
                all_cam_features = torch.cat(all_cam_features, dim=1)
                identity = all_cam_features
                all_cam_features = nets['policy']['film_fusion'](all_cam_features, lang_embed)
                obs_cond = all_cam_features + identity
                obs_cond = torch.cat([obs_cond, qpos], dim=1)
            else:
                obs_cond = torch.cat(all_cam_features + [qpos], dim=1)
            obs_cond = nets['policy']['combine'](obs_cond)

            # initialize action from Guassian noise
            noisy_action = torch.randn(
                (B, Tp, action_dim), device=obs_cond.device)
            naction = noisy_action

            # init scheduler
            self.noise_scheduler.set_timesteps(self.num_inference_timesteps)

            for k in self.noise_scheduler.timesteps:
                # predict noise
                noise_pred = nets['policy']['noise_pred_net'](
                    x=naction,
                    t=k,
                    global_cond=obs_cond,
                )


                # inverse diffusion step (remove noise)
                naction = self.noise_scheduler.step(
                    model_output=noise_pred,
                    timestep=k,
                    sample=naction
                ).prev_sample

            return naction

    def serialize(self):
        return {
            "nets": self.nets.state_dict(),
            "ema": self.ema.averaged_model.state_dict() if self.ema is not None else None,
        }

    def deserialize(self, model_dict, inference=False,steps=0,pre_ckpt=False):
        skip_ckpt_name = ["policy.noise_pred_net.final_layer.linear.weight",
                          "policy.noise_pred_net.final_layer.linear.bias",
                          'policy.combine.0.weight',
                          'policy.combine.0.bias',
                          "policy.noise_pred_net.x_embedder.weight",
                          "policy.noise_pred_net.x_embedder.bias"]
        if not inference:
            if pre_ckpt:
                print("load aloha ckpt>>>>>>>>>>>>>>>>>>>>>")
                new_dict = {}
                for k, v in self.nets.state_dict().items():
                    if k  in skip_ckpt_name:
                        new_dict[k] = v
                        print("skipp name>>>>>>>>>>>>>>>>>>>>>", k)
                    else:
                        new_dict[k] = model_dict["nets"][k]
                        print("load name>>>>>>>>>>>>>>>>>>>>>", k)
                status = self.nets.load_state_dict(new_dict)
            else:
                status = self.nets.load_state_dict(model_dict["nets"])
            print('Loaded model for training')
        else:
            status = None
            assert model_dict.get("ema", None) is not None, "Inference requires EMA model"
            print("==============Inference Time================")
            print('Do not load model ckpt from nets, only from ema')
        if model_dict.get("ema", None) is not None:
            print('Loaded EMA')
            if pre_ckpt:
                ema_new_dict={}
                print("load_aloha_ckpt_ema model>>>>>>>>>>>>>>>>>>>>>",ema_new_dict)
                for k2, v2 in self.ema.averaged_model.state_dict().items():
                    if k2 in skip_ckpt_name:
                        ema_new_dict[k2] = v2
                    else:
                        ema_new_dict[k2] = model_dict["ema"][k2]
                status_ema = self.ema.averaged_model.load_state_dict(ema_new_dict)
            else:
                status_ema = self.ema.averaged_model.load_state_dict(model_dict["ema"])
            self.ema.optimization_step=steps
            status = [status, status_ema]
        return status


class Attention(nn.Module):
    fused_attn: Final[bool]

    def __init__(
            self,
            dim: int,
            num_heads: int = 8,
            qkv_bias: bool = False,
            qk_norm: bool = False,
            attn_drop: float = 0.,
            proj_drop: float = 0.,
            norm_layer: nn.Module = nn.LayerNorm,
    ) -> None:
        super().__init__()
        assert dim % num_heads == 0, 'dim should be divisible by num_heads'
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        self.fused_attn = use_fused_attn()

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.q_norm = norm_layer(self.head_dim) if qk_norm else nn.Identity()
        self.k_norm = norm_layer(self.head_dim) if qk_norm else nn.Identity()
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x: torch.Tensor, attn_mask=None) -> torch.Tensor:
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv.unbind(0)
        q, k = self.q_norm(q), self.k_norm(k)

        if self.fused_attn:
            x = F.scaled_dot_product_attention(
                q, k, v, attn_mask=attn_mask,
                dropout_p=self.attn_drop.p if self.training else 0.,
            )
        else:
            q = q * self.scale
            attn = q @ k.transpose(-2, -1)
            if attn_mask is not None:
                attn += attn_mask
            attn = attn.softmax(dim=-1)
            attn = self.attn_drop(attn)
            x = attn @ v

        x = x.transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


def modulate(x, shift, scale):
    return x * (1 + scale.unsqueeze(1)) + shift.unsqueeze(1)


#################################################################################
#               Embedding Layers for Timesteps and Class Labels                 #
#################################################################################

class TimestepEmbedder(nn.Module):
    """
    Embeds scalar timesteps into vector representations.
    """

    def __init__(self, hidden_size, frequency_embedding_size=256):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(frequency_embedding_size, hidden_size, bias=True),
            nn.SiLU(),
            nn.Linear(hidden_size, hidden_size, bias=True),
        )
        self.frequency_embedding_size = frequency_embedding_size

    @staticmethod
    def timestep_embedding(t, dim, max_period=10000):
        """
        Create sinusoidal timestep embeddings.
        :param t: a 1-D Tensor of N indices, one per batch element.
                          These may be fractional.
        :param dim: the dimension of the output.
        :param max_period: controls the minimum frequency of the embeddings.
        :return: an (N, D) Tensor of positional embeddings.
        """
        # https://github.com/openai/glide-text2im/blob/main/glide_text2im/nn.py
        half = dim // 2
        freqs = torch.exp(
            -math.log(max_period) * torch.arange(start=0, end=half, dtype=torch.float32) / half
        ).to(device=t.device)
        args = t[:, None].float() * freqs[None]
        embedding = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        if dim % 2:
            embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
        return embedding

    def forward(self, t):
        t_freq = self.timestep_embedding(t, self.frequency_embedding_size)
        t_emb = self.mlp(t_freq)
        return t_emb


#################################################################################
#                                 Core DiT Model                                #
#################################################################################

class DiTBlock(nn.Module):
    """
    A DiT block with adaptive layer norm zero (adaLN-Zero) conditioning.
    """

    def __init__(self, hidden_size, num_heads, mlp_ratio=4.0, **block_kwargs):
        super().__init__()
        self.norm1 = nn.LayerNorm(hidden_size, elementwise_affine=False, eps=1e-6)
        self.attn = Attention(hidden_size, num_heads=num_heads, qkv_bias=True, **block_kwargs)
        self.norm2 = nn.LayerNorm(hidden_size, elementwise_affine=False, eps=1e-6)
        mlp_hidden_dim = int(hidden_size * mlp_ratio)
        approx_gelu = lambda: nn.GELU(approximate="tanh")


        self.mlp = Mlp(in_features=hidden_size, hidden_features=mlp_hidden_dim, act_layer=approx_gelu, drop=0)

        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(),
            nn.Linear(hidden_size, 6 * hidden_size, bias=True)
        )

    def forward(self, x, c, attn_mask=None):
        shift_msa, scale_msa, gate_msa, shift_mlp, scale_mlp, gate_mlp = self.adaLN_modulation(c).chunk(6, dim=1)
        x = x + gate_msa.unsqueeze(1) * self.attn(modulate(self.norm1(x), shift_msa, scale_msa), attn_mask=attn_mask)
        x = x + gate_mlp.unsqueeze(1) * self.mlp(modulate(self.norm2(x), shift_mlp, scale_mlp))

        return x


class FinalLayer(nn.Module):
    """
    The final layer of DiT.
    """

    def __init__(self, hidden_size, output_dim):
        super().__init__()
        self.norm_final = nn.LayerNorm(hidden_size, elementwise_affine=False, eps=1e-6)
        self.linear = nn.Linear(hidden_size, output_dim, bias=True)
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(),
            nn.Linear(hidden_size, 2 * hidden_size, bias=True)
        )

    def forward(self, x, c):
        shift, scale = self.adaLN_modulation(c).chunk(2, dim=1)
        x = modulate(self.norm_final(x), shift, scale)
        x = self.linear(x)
        return x


class DiT(nn.Module):
    """
    Diffusion model with a Transformer backbone.
    """

    def __init__(
            self,
            input_dim: int = 10,  # action dim
            output_dim: int = 10,  # action dim
            cond_dim: int = 0,  # the input dim of the condition
            horizon: int = 10,  # horizon


            depth: int = 28,  # number of DiT blocks
            n_emb: int = 256,  # embedding size
            num_heads: int = 16,
            mlp_ratio: int = 4.0,
            time_as_cond: bool = True,
            obs_as_cond: bool = True,
            learn_sigma: bool = False,
    ):
        super().__init__()
        # compute number of tokens for main trunk and condition encoder
        T = horizon
        if not time_as_cond:
            T += 1
        obs_as_cond = cond_dim > 0
        if obs_as_cond:
            assert time_as_cond
        # mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
        # mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
        # self.register_buffer("mask", mask)

        self.learn_sigma = learn_sigma
        self.input_dim = input_dim
        self.output_dim = output_dim * 2 if learn_sigma else output_dim
        self.num_heads = num_heads

        self.x_embedder = nn.Linear(input_dim, n_emb)
        self.t_embedder = TimestepEmbedder(n_emb)
        self.cond_obs_emb = None
        if obs_as_cond:
            self.cond_obs_emb = nn.Linear(cond_dim, n_emb)

        # Will use fixed sin-cos embedding:
        self.pos_embed = nn.Parameter(torch.zeros(1, horizon, n_emb))

        self.blocks = nn.ModuleList([
            DiTBlock(n_emb, num_heads, mlp_ratio=mlp_ratio) for _ in range(depth)
        ])
        self.final_layer = FinalLayer(n_emb, output_dim=output_dim)
        self.initialize_weights()
        # constants
        self.horizon = horizon
        self.time_as_cond = time_as_cond
        self.obs_as_cond = obs_as_cond

    def  count_parameters(self):
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        return total_params, trainable_params


    def initialize_weights(self):
        # Initialize transformer layers:
        def _basic_init(module):
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

        self.apply(_basic_init)

        # Initialize (and freeze) pos_embed by sin-cos embedding:
        nn.init.normal_(self.pos_embed, mean=0.0, std=0.02)

        # Initialize patch_embed like nn.Linear (instead of nn.Conv2d):
        w = self.x_embedder.weight.data
        nn.init.xavier_uniform_(w.view([w.shape[0], -1]))
        nn.init.constant_(self.x_embedder.bias, 0)

        # Initialize label embedding table:
        nn.init.normal_(self.cond_obs_emb.weight, mean=0.0, std=0.02)
        nn.init.constant_(self.cond_obs_emb.bias, 0)

        # Initialize timestep embedding MLP:
        nn.init.normal_(self.t_embedder.mlp[0].weight, std=0.02)
        nn.init.normal_(self.t_embedder.mlp[2].weight, std=0.02)

        # Zero-out adaLN modulation layers in DiT blocks:
        for block in self.blocks:
            nn.init.constant_(block.adaLN_modulation[-1].weight, 0)
            nn.init.constant_(block.adaLN_modulation[-1].bias, 0)

        # Zero-out output layers:
        nn.init.constant_(self.final_layer.adaLN_modulation[-1].weight, 0)
        nn.init.constant_(self.final_layer.adaLN_modulation[-1].bias, 0)
        nn.init.constant_(self.final_layer.linear.weight, 0)
        # torch.nn.init.xavier_uniform_(self.final_layer.linear.weight)
        nn.init.constant_(self.final_layer.linear.bias, 0)

    def get_optim_groups(self, weight_decay: float = 1e-3):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, Attention)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = "%s.%s" % (mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.startswith("bias"):
                    # MultiheadAttention bias starts with "bias"
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)


        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert (
                len(inter_params) == 0
        ), "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert (
                len(param_dict.keys() - union_params) == 0
        ), "parameters %s were not separated into either decay/no_decay set!" % (
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(list(decay))],
                "weight_decay": weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(list(no_decay))],
                "weight_decay": 0.0,
            },
        ]
        return optim_groups

    def configure_optimizers(self,
                             learning_rate: float = 1e-4,
                             weight_decay: float = 1e-3,
                             betas: Tuple[float, float] = (0.9, 0.95)):
        optim_groups = self.get_optim_groups(weight_decay=weight_decay)
        optimizer = torch.optim.AdamW(
            optim_groups, lr=learning_rate, betas=betas
        )
        return optimizer

    def forward(self, x, t, global_cond):
        """
        Forward pass of DiT.
        x: (N, T, input_dim)
        t: (N,) tensor of diffusion timesteps
        global_cond: (N, n_obs_steps, D) tensor of conditions: image embeddings
        """
        t = t
        if not torch.is_tensor(t):
            # TODO: this requires sync between CPU and GPU. So try to pass timesteps as tensors if you can
            t = torch.tensor([t], dtype=torch.long, device=x.device)
        elif torch.is_tensor(t) and len(t.shape) == 0:
            t = t[None].to(x.device)
        t = t.expand(t.shape[0])
        x = self.x_embedder(x) + self.pos_embed  # (N, T, D), where T = horizon
        t = self.t_embedder(t)  # (N, D)
        if self.obs_as_cond:
            global_cond = self.cond_obs_emb(global_cond)  # (N, D)

        c = t + (global_cond.sum(dim=1) if len(global_cond.shape) == 3 else global_cond)  # (N, D)
        for block in self.blocks:
            # x = block(x, c, attn_mask=self.mask)  # (N, T, D)
            x = block(x, c, attn_mask=None )  # (N, T, D)
        x = self.final_layer(x, c)  # (N, T, output_dim)
        return x


#################################################################################
#                                   DiT Configs                                  #
#################################################################################


def DiT_XH(**kwargs):
    return DiT(depth=36, n_emb=1760, num_heads=32, **kwargs)
def DiT_H(**kwargs):
    return DiT(depth=32, n_emb=1280, num_heads=16, **kwargs)


def DiT_L(**kwargs):
    return DiT(depth=24, n_emb=1024, num_heads=16, **kwargs)


def DiT_B(**kwargs):
    return DiT(depth=12, n_emb=768, num_heads=12, **kwargs)


def DiT_S(**kwargs):
    return DiT(depth=12, n_emb=384, num_heads=6, **kwargs)


def DiT_Ti(**kwargs):
    return DiT(depth=8, n_emb=256, num_heads=4, **kwargs)


DiT_models = {
    "DiT-XH":DiT_XH,'DiT-H': DiT_H, 'DiT-L': DiT_L, 'DiT-B': DiT_B, 'DiT-S': DiT_S, "DiT-Ti": DiT_Ti
}
