import warnings
import random
import logging
import numpy as np



try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

import torch
import torch.nn as nn
import torch.utils.checkpoint

import torchvision.transforms as transforms
import torchvision.transforms.functional as TVF

from torch.nn import functional as F
from torchvision.transforms import Lambda, Compose




class ColorRandomizer(nn.Module):
    """
    Randomly sample color jitter at input, and then average across color jtters at output.
    """

    def __init__(
            self,
            input_shape,
            brightness=0.3,
            contrast=0.3,
            saturation=0.3,
            hue=0.3,
            num_samples=1,
    ):
        """
        Args:
            input_shape (tuple, list): shape of input (not including batch dimension)
            brightness (None or float or 2-tuple): How much to jitter brightness. brightness_factor is chosen uniformly
                from [max(0, 1 - brightness), 1 + brightness] or the given [min, max]. Should be non negative numbers.
            contrast (None or float or 2-tuple): How much to jitter contrast. contrast_factor is chosen uniformly
                from [max(0, 1 - contrast), 1 + contrast] or the given [min, max]. Should be non negative numbers.
            saturation (None or float or 2-tuple): How much to jitter saturation. saturation_factor is chosen uniformly
                from [max(0, 1 - saturation), 1 + saturation] or the given [min, max]. Should be non negative numbers.
            hue (None or float or 2-tuple): How much to jitter hue. hue_factor is chosen uniformly from [-hue, hue] or
                the given [min, max]. Should have 0<= hue <= 0.5 or -0.5 <= min <= max <= 0.5. To jitter hue, the pixel
                values of the input image has to be non-negative for conversion to HSV space; thus it does not work
                if you normalize your image to an interval with negative values, or use an interpolation that
                generates negative values before using this function.
            num_samples (int): number of random color jitters to take
        """
        super(ColorRandomizer, self).__init__()

        assert len(input_shape) == 3  # (C, H, W)

        self.input_shape = input_shape
        self.brightness = [max(0, 1 - brightness), 1 + brightness] if type(brightness) in {float, int} else brightness
        self.contrast = [max(0, 1 - contrast), 1 + contrast] if type(contrast) in {float, int} else contrast
        self.saturation = [max(0, 1 - saturation), 1 + saturation] if type(saturation) in {float, int} else saturation
        self.hue = [-hue, hue] if type(hue) in {float, int} else hue
        self.num_samples = num_samples

    @torch.jit.unused
    def get_transform(self):
        """
        Get a randomized transform to be applied on image.

        Implementation taken directly from:

        https://github.com/pytorch/vision/blob/2f40a483d73018ae6e1488a484c5927f2b309969/torchvision/transforms/transforms.py#L1053-L1085

        Returns:
            Transform: Transform which randomly adjusts brightness, contrast and
            saturation in a random order.
        """
        transforms = []

        if self.brightness is not None:
            brightness_factor = random.uniform(self.brightness[0], self.brightness[1])
            transforms.append(Lambda(lambda img: TVF.adjust_brightness(img, brightness_factor)))

        if self.contrast is not None:
            contrast_factor = random.uniform(self.contrast[0], self.contrast[1])
            transforms.append(Lambda(lambda img: TVF.adjust_contrast(img, contrast_factor)))

        if self.saturation is not None:
            saturation_factor = random.uniform(self.saturation[0], self.saturation[1])
            transforms.append(Lambda(lambda img: TVF.adjust_saturation(img, saturation_factor)))

        if self.hue is not None:
            hue_factor = random.uniform(self.hue[0], self.hue[1])
            transforms.append(Lambda(lambda img: TVF.adjust_hue(img, hue_factor)))

        random.shuffle(transforms)
        transform = Compose(transforms)

        return transform

    def get_batch_transform(self, N):
        """
        Generates a batch transform, where each set of sample(s) along the batch (first) dimension will have the same
        @N unique ColorJitter transforms applied.

        Args:
            N (int): Number of ColorJitter transforms to apply per set of sample(s) along the batch (first) dimension

        Returns:
            Lambda: Aggregated transform which will autoamtically apply a different ColorJitter transforms to
                each sub-set of samples along batch dimension, assumed to be the FIRST dimension in the inputted tensor
                Note: This function will MULTIPLY the first dimension by N
        """
        return Lambda(lambda x: torch.stack([self.get_transform()(x_) for x_ in x for _ in range(N)]))

    def output_shape_in(self, input_shape=None):
        # outputs are same shape as inputs
        return list(input_shape)

    def output_shape_out(self, input_shape=None):
        # since the forward_out operation splits [B * N, ...] -> [B, N, ...]
        # and then pools to result in [B, ...], only the batch dimension changes,
        # and so the other dimensions retain their shape.
        return list(input_shape)

    def _forward_in(self, inputs):
        """
        Samples N random color jitters for each input in the batch, and then reshapes
        inputs to [B * N, ...].
        """
        assert len(inputs.shape) >= 3  # must have at least (C, H, W) dimensions

        # Make sure shape is exactly 4
        if len(inputs.shape) == 3:
            inputs = torch.unsqueeze(inputs, dim=0)

        # TODO: Make more efficient other than implicit for-loop?
        # Create lambda to aggregate all color randomizings at once
        transform = self.get_batch_transform(N=self.num_samples)

        return transform(inputs)

    # def _forward_out(self, inputs):
    #     """
    #     Splits the outputs from shape [B * N, ...] -> [B, N, ...] and then average across N
    #     to result in shape [B, ...] to make sure the network output is consistent with
    #     what would have happened if there were no randomization.
    #     """
    #     batch_size = (inputs.shape[0] // self.num_samples)
    #     out = TensorUtils.reshape_dimensions(inputs, begin_axis=0, end_axis=0, target_dims=(batch_size, self.num_samples))
    #     return out.mean(dim=1)

    # def _visualize(self, pre_random_input, randomized_input, num_samples_to_visualize=2):
    #     batch_size = pre_random_input.shape[0]
    #     random_sample_inds = torch.randint(0, batch_size, size=(num_samples_to_visualize,))
    #     pre_random_input_np = TensorUtils.to_numpy(pre_random_input)[random_sample_inds]
    #     randomized_input = TensorUtils.reshape_dimensions(
    #         randomized_input,
    #         begin_axis=0,
    #         end_axis=0,
    #         target_dims=(batch_size, self.num_samples)
    #     )  # [B * N, ...] -> [B, N, ...]
    #     randomized_input_np = TensorUtils.to_numpy(randomized_input[random_sample_inds])

    #     pre_random_input_np = pre_random_input_np.transpose((0, 2, 3, 1))  # [B, C, H, W] -> [B, H, W, C]
    #     randomized_input_np = randomized_input_np.transpose((0, 1, 3, 4, 2))  # [B, N, C, H, W] -> [B, N, H, W, C]

    #     visualize_image_randomizer(
    #         pre_random_input_np,
    #         randomized_input_np,
    #         randomizer_name='{}'.format(str(self.__class__.__name__))
    #     )

    def __repr__(self):
        """Pretty print network."""
        header = '{}'.format(str(self.__class__.__name__))
        msg = header + f"(input_shape={self.input_shape}, brightness={self.brightness}, contrast={self.contrast}, " \
                       f"saturation={self.saturation}, hue={self.hue}, num_samples={self.num_samples})"
        return msg


class DroidTransformerPolicy(nn.Module):
    def __init__(self, args_override, rank=None):
        from collections import OrderedDict
        from robomimic.models.base_nets import ResNet18Conv, SpatialSoftmax
        from robomimic.algo.diffusion_policy import replace_bn_with_gn, ConditionalUnet1D

        from diffusers.schedulers.scheduling_ddpm import DDPMScheduler
        from diffusers.schedulers.scheduling_ddim import DDIMScheduler
        from diffusers.training_utils import EMAModel

        super().__init__()

        self.camera_names = args_override['camera_names']

        self.observation_horizon = args_override['observation_horizon']  ### TODO TODO TODO DO THIS
        self.prediction_horizon = args_override['prediction_horizon']  # chunk size
        # self.num_inference_timesteps = args_override['num_inference_timesteps']

        self.num_inference_timesteps = 10  # 30 #15 #30 #10 #15 #30 #50 #100 #16 #args_override['num_inference_timesteps']

        self.ema_power = args_override['ema_power']  # 0.75
        self.lr = args_override['lr']  # 1e-4
        self.weight_decay = args_override['weight_decay']  # 0.0 # 1e-6

        self.pool_class = args_override['pool_class']  # null
        # self.img_flatten = args_override['img_flatten'] # True
        # for pool spatial softmax num_kp
        self.stsm_num_kp = args_override['stsm_num_kp']  # 512

        self.img_fea_dim = args_override['img_fea_dim']  # 512
        self.ac_dim = args_override['action_dim']  # 14 + 2
        self.state_dim = args_override['state_dim']  # 14 + 2

        self.num_queries = args_override['num_queries']
        self.num_noise_samples = args_override['num_noise_samples']  # 8

        print(args_override)
        self.double_arm = args_override['double_arm']

        # have aug data in dataset!
        self.use_color_rand = False
        if self.use_color_rand:
            self.color_randomizer = ColorRandomizer(input_shape=[3, 270, 480])
        else:
            self.color_randomizer = None

        #### init backbone ####
        from detr.models.backbone import build_backbone
        import argparse
        from detr.main import get_args_parser
        parser = argparse.ArgumentParser('DETR training and evaluation script', parents=[get_args_parser()])
        diff_args = parser.parse_args()
        for k, v in args_override.items():
            setattr(diff_args, k, v)

        self.diff_args = diff_args
        if "clip" in args_override['backbone']:
            self.norm = False
        else:
            self.norm = True
        print("backbone name !!", args_override['backbone'], "film" in args_override['backbone'])
        self.use_film = "film" in args_override['backbone']
        self.use_lang = args_override['use_lang']
        self.backbone_name = args_override['backbone']
        self.image_size_raw_h = int(args_override['image_size_h'])
        self.image_size_raw_w = int(args_override['image_size_w'])
        self.image_size_h = int(args_override['image_size_h']) / 32  # type=int,default=480)
        self.image_size_w = int(args_override['image_size_w']) / 32,  # type=int,default=640)
        self.image_size_h = int(np.ceil(self.image_size_h))
        self.image_size_w = int(np.ceil(self.image_size_w))

        backbones = []
        pools = []
        linears = []
        for _ in self.camera_names:
            backbone = build_backbone(diff_args)
            backbones.append(backbone)

            if self.pool_class == 'SpatialSoftmax':
                if self.backbone_name == 'resnet50':
                    # [2048, 9, 15] for 270 480 droid, resnet 50
                    input_shape = [2048, self.image_size_h, self.image_size_w]  # 640 480

                elif self.backbone_name == 'resnet18':
                    # [512, 9, 15] for 270 480 droid, resnet 18
                    input_shape = [512, self.image_size_h, self.image_size_w]
                elif self.backbone_name == "resnet50film":
                    input_shape = [2048, self.image_size_h, self.image_size_w]
                    input_shape = [1536, 10, 10]
                elif self.backbone_name == "efficientnet_b5film":
                    input_shape = [2048, 15, 15]
                elif self.backbone_name == "efficientnet_b3film":
                    input_shape = [1536, 10, 10]
                else:
                    input_shape = [2048, self.image_size_h, self.image_size_w]
                    # input_shape = [1536, 10,10]

                pools.append(
                    nn.Sequential(
                        SpatialSoftmax(**{'input_shape': input_shape, 'num_kp': self.stsm_num_kp, 'temperature': 1.0,
                                          'learnable_temperature': False, 'noise_std': 0.0}),
                        nn.Flatten(start_dim=1, end_dim=-1)
                    )
                )
                linears.append(
                    nn.Sequential(
                        nn.Linear(int(np.prod([self.stsm_num_kp, 2])), self.stsm_num_kp),
                        nn.ReLU(),
                        nn.Linear(self.stsm_num_kp, self.img_fea_dim)
                    )
                )
            elif self.pool_class == 'null':
                pools.append(
                    nn.Sequential(
                        nn.Conv2d(backbones[0].num_channels, 1024, kernel_size=3, padding='same'),
                        # nn.LayerNorm(1024),
                        nn.ReLU(),
                        nn.Conv2d(1024, 256, kernel_size=3, padding='same'),
                        # nn.LayerNorm(256),
                        nn.ReLU(),
                        nn.Conv2d(256, 64, kernel_size=3, padding='same'),
                        # nn.LayerNorm(64),
                        nn.ReLU(),
                        nn.Conv2d(64, 8, kernel_size=1),
                        nn.Flatten(start_dim=1, end_dim=-1)
                    )
                )
                image_flatten_dim = 8 *self.image_size_h  * self.image_size_w 
                linears.append(torch.nn.Linear(image_flatten_dim, self.img_fea_dim))

        backbones = nn.ModuleList(backbones)
        pools = nn.ModuleList(pools)
        linears = nn.ModuleList(linears)

        self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim

        self.cond_obs_dim = args_override['cond_obs_dim']  # 512
        self.combine = nn.Sequential(
            nn.Linear(self.obs_input_dim, 1024),
            nn.ReLU(),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, self.cond_obs_dim)
        )
        if "resnet" in self.backbone_name:
            backbones = replace_bn_with_gn(backbones)  # TODO

        # hidden_dim = 512
        # if "clip" in self.backbone_name:
        #     self.input_proj = nn.Linear(backbones[0].num_channels, hidden_dim)
        # else:
        #     self.input_proj = nn.Conv2d(backbones[0].num_channels, hidden_dim, kernel_size=1)
        print(args_override)
        print(f'Build noise_pred_net Droid Transformer!')
        print(f"noise_pred_net Droid Transformer cond obs dim: {self.cond_obs_dim}")
        noise_pred_net = TransformerForDiffusion(
            input_dim=self.ac_dim,
            output_dim=self.ac_dim,
            cond_dim=self.cond_obs_dim,
            horizon=self.prediction_horizon,
            n_obs_steps=1,
            n_layer=8,
            n_head=4,
            n_cond_layers=0,
            n_emb=256,
            p_drop_emb=0.0,
            p_drop_attn=0.3,
            causal_attn=True,
            time_as_cond=True,
            obs_as_cond=True
        )

        nets = nn.ModuleDict({
            'policy': nn.ModuleDict({
                'backbones': backbones,
                'pools': pools,
                'linears': linears,
                'combine': self.combine,
                'noise_pred_net': noise_pred_net
            })
        })

        # nets = nets.float().cuda()
        # ENABLE_EMA = False
        ENABLE_EMA = True
        if ENABLE_EMA:
            ema = EMAModel(model=nets.cuda(), power=self.ema_power)  # .to(device=rank)
            # ema  = ema.to(rank)
            # ema  = DDP(ema , device_ids=[rank])
        else:
            ema = None
        self.nets = nets
        self.ema = ema

        self.noise_scheduler = DDIMScheduler(
            num_train_timesteps=100,
            beta_schedule='squaredcos_cap_v2',
            clip_sample=True,
            set_alpha_to_one=True,
            steps_offset=0,
            prediction_type='epsilon'
        )

        n_parameters = sum(p.numel() for p in self.parameters())
        print(f"droid_Transformer number of parameters: %.2fM" % (n_parameters / 1e6,))

    def configure_optimizers(self):
        param_dicts = [
            {"params": [p for n, p in self.nets.named_parameters() if "backbone" not in n and p.requires_grad]},
            {
                "params": [p for n, p in self.nets.named_parameters() if "backbone" in n and p.requires_grad],
                "lr": self.diff_args.lr_backbone,
            },
        ]
        optimizer = torch.optim.AdamW(param_dicts, lr=self.lr,
                                      weight_decay=self.weight_decay)

        return optimizer

    def __call__(self, qpos, image, actions=None, is_pad=None, language_distilbert=None):
        lang_embed = language_distilbert
        B = qpos.shape[0]
        normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                         std=[0.229, 0.224, 0.225])
        # language_distilbert: [1, 768]
        if self.norm:
            image = normalize(image)
        if actions is not None:  # training time
            nets = self.nets

            actions = actions[:, :self.num_queries, :self.ac_dim]
            is_pad = is_pad[:, :self.num_queries]
            # actions size: torch.Size([64, 50, 10])
            # print(f"actions size: {actions.size()}")

            all_cam_features = []
            all_cam_pos = []
            for cam_id, cam_name in enumerate(self.camera_names):
                if self.use_film and lang_embed is not None:
                    cur_img = image[:, cam_id]
                    # print(f"1. cur_img: {cur_img.size()}")
                    if self.color_randomizer is not None:
                        cur_img = self.color_randomizer._forward_in(cur_img)
                        # print(f"2. cur_img: {cur_img.size()}")
                    # print("lang_embed!!",lang_embed.shape)
                    # print("cur_imag!!",cur_img.shape)#(bs,768)
                    language_embed = lang_embed
                    features, pos = self.nets['policy']['backbones'][cam_id](cur_img, language_embed)
                else:
                    cur_img = image[:, cam_id]
                    # cur_img: torch.Size([64, 3, 270, 480])
                    if self.color_randomizer is not None:
                        cur_img = self.color_randomizer._forward_in(cur_img)
                    features, pos = self.nets['policy']['backbones'][cam_id](cur_img)

                features = features[0]  # take the last layer feature
                pos = pos[0]
                # resnet18: cam_id: 0, features size: torch.Size([64, 512, 9, 15])
                # resnet18: cam_id: 0, pos size: torch.Size([1, 256, 9, 15])
                cam_features = features

                pool_features = nets['policy']['pools'][cam_id](cam_features) # resnet18: 1. pool_features: torch.Size([64, 512, 2])
                out_features = nets['policy']['linears'][cam_id](pool_features)

                all_cam_features.append(out_features)
                all_cam_pos.append(pos)

            # qpos size: torch.Size([64, 7])
            obs_cond = torch.cat(all_cam_features + [qpos], dim=1)  # 1. obs_cond size: torch.Size([64, 1031])
            obs_cond = self.combine(obs_cond)  # 2. obs_cond size: torch.Size([64, 512])

            # sample noise to add to actions
            noise = torch.randn([self.num_noise_samples] + list(actions.shape), device=obs_cond.device)

            # sample a diffusion iteration for each data point
            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps,
                (B,), device=obs_cond.device
            ).long()

            # add noise to the clean actions according to the noise magnitude at each diffusion iteration
            timesteps, noise = timesteps.to(obs_cond.device), noise.to(obs_cond.device)

            noisy_actions = torch.cat([self.noise_scheduler.add_noise(
                actions, noise[i], timesteps)
                for i in range(len(noise))], dim=0)  # noisy_actions size: torch.Size([512, 50, 10])

            obs_cond = obs_cond.repeat(self.num_noise_samples, 1) # obs_cond size: torch.Size([512, 1031])
            timesteps = timesteps.repeat(self.num_noise_samples)  # timesteps size: torch.Size([512])
            # is_pad = is_pad.repeat(self.num_noise_samples, 1)

            # predict the noise residual
            noise_pred = nets['policy']['noise_pred_net'](noisy_actions, timesteps, global_cond=obs_cond)

            # L2 loss
            noise = noise.view(noise.size(0) * noise.size(1), *noise.size()[2:])

            loss = F.mse_loss(noise_pred, noise)

            loss_dict = {}
            loss_dict['l2_loss'] = loss
            loss_dict['loss'] = loss

            if self.training and self.ema is not None:
                self.ema.step(nets)
                # torch.distributed.barrier()
            return loss_dict
        else:  # inference time
            To = self.observation_horizon
            Tp = self.prediction_horizon
            action_dim = self.ac_dim

            nets = self.nets
            if self.ema is not None:
                nets = self.ema.averaged_model

            all_cam_features = []
            all_cam_pos = []
            for cam_id, cam_name in enumerate(self.camera_names):
                if self.use_film and lang_embed is not None:
                    features, pos = self.nets['policy']['backbones'][cam_id](image[:, cam_id], lang_embed)
                else:
                    features, pos = self.nets['policy']['backbones'][cam_id](image[:, cam_id])

                features = features[0]  # take the last layer feature
                pos = pos[0]
                # cam_features = self.input_proj(features)
                cam_features = features
                pool_features = nets['policy']['pools'][cam_id](cam_features)
                # pool_features = torch.flatten(pool_features, start_dim=1)
                out_features = nets['policy']['linears'][cam_id](pool_features)

                all_cam_features.append(out_features)
                all_cam_pos.append(pos)

            obs_cond = torch.cat(all_cam_features + [qpos], dim=1)
            obs_cond = self.combine(obs_cond)

            # initialize action from Guassian noise
            noisy_action = torch.randn(
                (B, Tp, action_dim), device=obs_cond.device)
            naction = noisy_action

            # init scheduler
            self.noise_scheduler.set_timesteps(self.num_inference_timesteps)

            for k in self.noise_scheduler.timesteps:
                # predict noise
                noise_pred = nets['policy']['noise_pred_net'](
                    sample=naction,
                    timestep=k,
                    global_cond=obs_cond
                )

                # inverse diffusion step (remove noise)
                naction = self.noise_scheduler.step(
                    model_output=noise_pred,
                    timestep=k,
                    sample=naction
                ).prev_sample

            return naction

    def serialize(self):
        return {
            "nets": self.nets.state_dict(),
            "ema": self.ema.averaged_model.state_dict() if self.ema is not None else None,
        }

    def deserialize(self, model_dict):
        status = self.nets.load_state_dict(model_dict["nets"])
        print('Loaded model')

        if model_dict.get("ema", None) is not None:
            print('Loaded EMA')
            status_ema = self.ema.averaged_model.load_state_dict(model_dict["ema"])
            status = [status, status_ema]
        return status

import math
from typing import Union, Optional, Tuple
import logging
import torch
import torch.nn as nn
# from robomimic.algo.diffusion_policy import *

logger = logging.getLogger(__name__)

class SinusoidalPosEmb(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, x):
        device = x.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = x[:, None] * emb[None, :]
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb


class ModuleAttrMixin(nn.Module):
    def __init__(self):
        super().__init__()
        self._dummy_variable = nn.Parameter()

    @property
    def device(self):
        return next(iter(self.parameters())).device

    @property
    def dtype(self):
        return next(iter(self.parameters())).dtype


class TransformerForDiffusion(ModuleAttrMixin):
    def __init__(self,
                 input_dim: int,
                 output_dim: int,
                 horizon: int,
                 n_obs_steps: int = None,
                 cond_dim: int = 0,
                 n_layer: int = 12,
                 n_head: int = 12,
                 n_emb: int = 768,
                 p_drop_emb: float = 0.1,
                 p_drop_attn: float = 0.1,
                 causal_attn: bool = False,
                 time_as_cond: bool = True,
                 obs_as_cond: bool = False,
                 n_cond_layers: int = 0
                 ) -> None:
        super().__init__()

        # compute number of tokens for main trunk and condition encoder
        if n_obs_steps is None:
            n_obs_steps = horizon

        T = horizon
        T_cond = 1
        if not time_as_cond:
            T += 1
            T_cond -= 1
        obs_as_cond = cond_dim > 0
        if obs_as_cond:
            assert time_as_cond
            T_cond += n_obs_steps

        # input embedding stem
        self.input_emb = nn.Linear(input_dim, n_emb)
        self.pos_emb = nn.Parameter(torch.zeros(1, T, n_emb))
        self.drop = nn.Dropout(p_drop_emb)

        # cond encoder
        self.time_emb = SinusoidalPosEmb(n_emb)
        self.cond_obs_emb = None

        if obs_as_cond:
            self.cond_obs_emb = nn.Linear(cond_dim, n_emb)

        self.cond_pos_emb = None
        self.encoder = None
        self.decoder = None
        encoder_only = False
        if T_cond > 0:
            self.cond_pos_emb = nn.Parameter(torch.zeros(1, T_cond, n_emb))
            if n_cond_layers > 0:
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=n_emb,
                    nhead=n_head,
                    dim_feedforward=4 * n_emb,
                    dropout=p_drop_attn,
                    activation='gelu',
                    batch_first=True,
                    norm_first=True
                )
                self.encoder = nn.TransformerEncoder(
                    encoder_layer=encoder_layer,
                    num_layers=n_cond_layers
                )
            else:
                self.encoder = nn.Sequential(
                    nn.Linear(n_emb, 4 * n_emb),
                    nn.Mish(),
                    nn.Linear(4 * n_emb, n_emb)
                )
            # decoder
            decoder_layer = nn.TransformerDecoderLayer(
                d_model=n_emb,
                nhead=n_head,
                dim_feedforward=4 * n_emb,
                dropout=p_drop_attn,
                activation='gelu',
                batch_first=True,
                norm_first=True  # important for stability
            )
            self.decoder = nn.TransformerDecoder(
                decoder_layer=decoder_layer,
                num_layers=n_layer
            )
        else:
            # encoder only BERT
            encoder_only = True

            encoder_layer = nn.TransformerEncoderLayer(
                d_model=n_emb,
                nhead=n_head,
                dim_feedforward=4 * n_emb,
                dropout=p_drop_attn,
                activation='gelu',
                batch_first=True,
                norm_first=True
            )
            self.encoder = nn.TransformerEncoder(
                encoder_layer=encoder_layer,
                num_layers=n_layer
            )

        # attention mask
        if causal_attn:
            # causal mask to ensure that attention is only applied to the left in the input sequence
            # torch.nn.Transformer uses additive mask as opposed to multiplicative mask in minGPT
            # therefore, the upper triangle should be -inf and others (including diag) should be 0.
            sz = T
            mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
            mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
            self.register_buffer("mask", mask)

            if time_as_cond and obs_as_cond:
                S = T_cond
                t, s = torch.meshgrid(
                    torch.arange(T),
                    torch.arange(S),
                    indexing='ij'
                )
                mask = t >= (s - 1)  # add one dimension since time is the first token in cond
                mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
                self.register_buffer('memory_mask', mask)
            else:
                self.memory_mask = None
        else:
            self.mask = None
            self.memory_mask = None

        # decoder head
        self.ln_f = nn.LayerNorm(n_emb)
        self.head = nn.Linear(n_emb, output_dim)

        # constants
        self.T = T
        self.T_cond = T_cond
        self.horizon = horizon
        self.time_as_cond = time_as_cond
        self.obs_as_cond = obs_as_cond
        self.encoder_only = encoder_only

        # init
        self.apply(self._init_weights)
        logger.info(
            "number of parameters: %e", sum(p.numel() for p in self.parameters())
        )

    def _init_weights(self, module):
        ignore_types = (nn.Dropout,
                        SinusoidalPosEmb,
                        nn.TransformerEncoderLayer,
                        nn.TransformerDecoderLayer,
                        nn.TransformerEncoder,
                        nn.TransformerDecoder,
                        nn.ModuleList,
                        nn.Mish,
                        nn.Sequential)
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.MultiheadAttention):
            weight_names = [
                'in_proj_weight', 'q_proj_weight', 'k_proj_weight', 'v_proj_weight']
            for name in weight_names:
                weight = getattr(module, name)
                if weight is not None:
                    torch.nn.init.normal_(weight, mean=0.0, std=0.02)

            bias_names = ['in_proj_bias', 'bias_k', 'bias_v']
            for name in bias_names:
                bias = getattr(module, name)
                if bias is not None:
                    torch.nn.init.zeros_(bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif isinstance(module, TransformerForDiffusion):
            torch.nn.init.normal_(module.pos_emb, mean=0.0, std=0.02)
            if module.cond_obs_emb is not None:
                torch.nn.init.normal_(module.cond_pos_emb, mean=0.0, std=0.02)
        elif isinstance(module, ignore_types):
            # no param
            pass
        else:
            raise RuntimeError("Unaccounted module {}".format(module))

    def get_optim_groups(self, weight_decay: float = 1e-3):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, torch.nn.MultiheadAttention)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = "%s.%s" % (mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.startswith("bias"):
                    # MultiheadAttention bias starts with "bias"
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add("pos_emb")
        no_decay.add("_dummy_variable")
        if self.cond_pos_emb is not None:
            no_decay.add("cond_pos_emb")

        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert (
                len(inter_params) == 0
        ), "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert (
                len(param_dict.keys() - union_params) == 0
        ), "parameters %s were not separated into either decay/no_decay set!" % (
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(list(decay))],
                "weight_decay": weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(list(no_decay))],
                "weight_decay": 0.0,
            },
        ]
        return optim_groups

    def configure_optimizers(self,
                             learning_rate: float = 1e-4,
                             weight_decay: float = 1e-3,
                             betas: Tuple[float, float] = (0.9, 0.95)):
        optim_groups = self.get_optim_groups(weight_decay=weight_decay)
        optimizer = torch.optim.AdamW(
            optim_groups, lr=learning_rate, betas=betas
        )
        return optimizer

    def forward(self,
                sample: torch.Tensor,
                timestep: Union[torch.Tensor, float, int],
                global_cond: Optional[torch.Tensor] = None, **kwargs):
        """
        sample: (B,T,input_dim)
        timestep: (B,) or int, diffusion step
        cond: (B,T',cond_dim)
        output: (B,T,input_dim)
        """
        # 1. time
        # print("sample: ", sample.size(), "timestep", timestep.size(), "cond", cond.size())
        timesteps = timestep
        if not torch.is_tensor(timesteps):
            # TODO: this requires sync between CPU and GPU. So try to pass timesteps as tensors if you can
            timesteps = torch.tensor([timesteps], dtype=torch.long, device=sample.device)
        elif torch.is_tensor(timesteps) and len(timesteps.shape) == 0:
            timesteps = timesteps[None].to(sample.device)
        # broadcast to batch dimension in a way that's compatible with ONNX/Core ML
        timesteps = timesteps.expand(sample.shape[0])
        time_emb = self.time_emb(timesteps).unsqueeze(1)
        # (B,1,n_emb)

        # process input
        input_emb = self.input_emb(sample)

        if self.encoder_only:
            # BERT
            token_embeddings = torch.cat([time_emb, input_emb], dim=1)
            t = token_embeddings.shape[1]
            position_embeddings = self.pos_emb[
                                  :, :t, :
                                  ]  # each position maps to a (learnable) vector
            x = self.drop(token_embeddings + position_embeddings)
            # (B,T+1,n_emb)
            x = self.encoder(src=x, mask=self.mask)
            # (B,T+1,n_emb)
            x = x[:, 1:, :]
            # (B,T,n_emb)
        else:
            # encoder
            cond_embeddings = time_emb
            if self.obs_as_cond:
                cond_obs_emb = self.cond_obs_emb(global_cond).unsqueeze(1)
                # print(cond_embeddings.shape, cond_obs_emb.shape)
                # (B,To,n_emb)
                cond_embeddings = torch.cat([cond_embeddings, cond_obs_emb], dim=1)
            tc = cond_embeddings.shape[1]
            position_embeddings = self.cond_pos_emb[
                                  :, :tc, :
                                  ]  # each position maps to a (learnable) vector
            x = self.drop(cond_embeddings + position_embeddings)
            x = self.encoder(x)
            memory = x
            # (B,T_cond,n_emb)

            # decoder
            token_embeddings = input_emb
            t = token_embeddings.shape[1]
            position_embeddings = self.pos_emb[
                                  :, :t, :
                                  ]  # each position maps to a (learnable) vector

            x = self.drop(token_embeddings + position_embeddings)
            # (B,T,n_emb)
            x = self.decoder(
                tgt=x,
                memory=memory,
                tgt_mask=self.mask,
                memory_mask=self.memory_mask
            )
            # (B,T,n_emb)

        # head
        x = self.ln_f(x)
        x = self.head(x)
        # (B,T,n_out)
        return x