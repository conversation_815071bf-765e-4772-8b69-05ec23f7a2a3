import torch.nn as nn
from torch.nn import functional as F
import torchvision.transforms as transforms
import torch
import numpy as np


from utils.fusion_modules import FiLM
import warnings
import logging

#### init backbone ####
from vision_net.backbone import build_backbone
from vision_net.film_backbon import build_film_backbone

from vision_net.robomimic_basenet import SpatialSoftmax
from vision_net.robomimic_diffusion_net import replace_bn_with_gn, ConditionalUnet1D


from diffusers.schedulers.scheduling_ddim import DDIMScheduler
from diffusers.training_utils import EMAModel
# Configure logging level for GLFW if it uses logging
logging.getLogger("glfw").setLevel(logging.ERROR)  # Ignores warnings and below

# Suppress specific future warnings from a library
warnings.filterwarnings('ignore', category=FutureWarning)

import random
import torchvision.transforms.functional as TVF
from torchvision.transforms import Lambda, Compose

class ColorRandomizer(nn.Module):
    """
    Randomly sample color jitter at input, and then average across color jtters at output.
    """
    def __init__(
        self,
        input_shape,
        brightness=0.3,
        contrast=0.3,
        saturation=0.3,
        hue=0.3,
        num_samples=1,
    ):
        """
        Args:
            input_shape (tuple, list): shape of input (not including batch dimension)
            brightness (None or float or 2-tuple): How much to jitter brightness. brightness_factor is chosen uniformly
                from [max(0, 1 - brightness), 1 + brightness] or the given [min, max]. Should be non negative numbers.
            contrast (None or float or 2-tuple): How much to jitter contrast. contrast_factor is chosen uniformly
                from [max(0, 1 - contrast), 1 + contrast] or the given [min, max]. Should be non negative numbers.
            saturation (None or float or 2-tuple): How much to jitter saturation. saturation_factor is chosen uniformly
                from [max(0, 1 - saturation), 1 + saturation] or the given [min, max]. Should be non negative numbers.
            hue (None or float or 2-tuple): How much to jitter hue. hue_factor is chosen uniformly from [-hue, hue] or
                the given [min, max]. Should have 0<= hue <= 0.5 or -0.5 <= min <= max <= 0.5. To jitter hue, the pixel
                values of the input image has to be non-negative for conversion to HSV space; thus it does not work
                if you normalize your image to an interval with negative values, or use an interpolation that
                generates negative values before using this function.
            num_samples (int): number of random color jitters to take
        """
        super(ColorRandomizer, self).__init__()

        assert len(input_shape) == 3 # (C, H, W)

        self.input_shape = input_shape
        self.brightness = [max(0, 1 - brightness), 1 + brightness] if type(brightness) in {float, int} else brightness
        self.contrast = [max(0, 1 - contrast), 1 + contrast] if type(contrast) in {float, int} else contrast
        self.saturation = [max(0, 1 - saturation), 1 + saturation] if type(saturation) in {float, int} else saturation
        self.hue = [-hue, hue] if type(hue) in {float, int} else hue
        self.num_samples = num_samples

    @torch.jit.unused
    def get_transform(self):
        """
        Get a randomized transform to be applied on image.

        Implementation taken directly from:

        https://github.com/pytorch/vision/blob/2f40a483d73018ae6e1488a484c5927f2b309969/torchvision/transforms/transforms.py#L1053-L1085

        Returns:
            Transform: Transform which randomly adjusts brightness, contrast and
            saturation in a random order.
        """
        transforms = []

        if self.brightness is not None:
            brightness_factor = random.uniform(self.brightness[0], self.brightness[1])
            transforms.append(Lambda(lambda img: TVF.adjust_brightness(img, brightness_factor)))

        if self.contrast is not None:
            contrast_factor = random.uniform(self.contrast[0], self.contrast[1])
            transforms.append(Lambda(lambda img: TVF.adjust_contrast(img, contrast_factor)))

        if self.saturation is not None:
            saturation_factor = random.uniform(self.saturation[0], self.saturation[1])
            transforms.append(Lambda(lambda img: TVF.adjust_saturation(img, saturation_factor)))

        if self.hue is not None:
            hue_factor = random.uniform(self.hue[0], self.hue[1])
            transforms.append(Lambda(lambda img: TVF.adjust_hue(img, hue_factor)))

        random.shuffle(transforms)
        transform = Compose(transforms)

        return transform

    def get_batch_transform(self, N):
        """
        Generates a batch transform, where each set of sample(s) along the batch (first) dimension will have the same
        @N unique ColorJitter transforms applied.

        Args:
            N (int): Number of ColorJitter transforms to apply per set of sample(s) along the batch (first) dimension

        Returns:
            Lambda: Aggregated transform which will autoamtically apply a different ColorJitter transforms to
                each sub-set of samples along batch dimension, assumed to be the FIRST dimension in the inputted tensor
                Note: This function will MULTIPLY the first dimension by N
        """
        return Lambda(lambda x: torch.stack([self.get_transform()(x_) for x_ in x for _ in range(N)]))

    def output_shape_in(self, input_shape=None):
        # outputs are same shape as inputs
        return list(input_shape)

    def output_shape_out(self, input_shape=None):
        # since the forward_out operation splits [B * N, ...] -> [B, N, ...]
        # and then pools to result in [B, ...], only the batch dimension changes,
        # and so the other dimensions retain their shape.
        return list(input_shape)

    def _forward_in(self, inputs):
        """
        Samples N random color jitters for each input in the batch, and then reshapes
        inputs to [B * N, ...].
        """
        assert len(inputs.shape) >= 3 # must have at least (C, H, W) dimensions

        # Make sure shape is exactly 4
        if len(inputs.shape) == 3:
            inputs = torch.unsqueeze(inputs, dim=0)

        # TODO: Make more efficient other than implicit for-loop?
        # Create lambda to aggregate all color randomizings at once
        transform = self.get_batch_transform(N=self.num_samples)

        return transform(inputs)

    def __repr__(self):
        """Pretty print network."""
        header = '{}'.format(str(self.__class__.__name__))
        msg = header + f"(input_shape={self.input_shape}, brightness={self.brightness}, contrast={self.contrast}, " \
                       f"saturation={self.saturation}, hue={self.hue}, num_samples={self.num_samples})"
        return msg


class DroidDiffusionPolicy(nn.Module):
    def __init__(self, args_override, rank=None):


        super().__init__()

        self.camera_names = args_override['camera_names']

        self.observation_horizon = args_override['observation_horizon'] ### TODO TODO TODO DO THIS
        self.prediction_horizon = args_override['prediction_horizon'] # chunk size
        #self.num_inference_timesteps = args_override['num_inference_timesteps']

        self.num_inference_timesteps = 10 #30 #15 #30 #10 #15 #30 #50 #100 #16 #args_override['num_inference_timesteps']

        self.ema_power = args_override['ema_power'] # 0.75
        self.lr = args_override['lr'] # 1e-4
        self.weight_decay = args_override['weight_decay'] #0.0 # 1e-6

        self.pool_class = args_override['pool_class'] # null

        self.stsm_num_kp = args_override['stsm_num_kp'] #512

        self.img_fea_dim = args_override['img_fea_dim'] #512
        self.ac_dim = args_override['action_dim'] # 14 + 2
        self.state_dim = args_override['state_dim'] # 14 + 2

        self.num_queries = args_override['num_queries']
        self.num_noise_samples = args_override['num_noise_samples'] # 8

        print(args_override)
        self.double_arm=args_override['double_arm']
        # self.double_arm=False
        # have aug data in dataset!
        self.use_color_rand = False
        if self.use_color_rand:
            self.color_randomizer = ColorRandomizer(input_shape=[3, 240, 320])
        else:
            self.color_randomizer = None


        if "clip" in args_override['backbone']:
            self.norm = False
        else:
            self.norm = True
        print("backbone name !!",args_override['backbone'],"film" in args_override['backbone'])
        self.use_film = "film" in args_override['backbone']
        self.use_filmfusion = args_override['use_filmfusion']
        self.use_lang = args_override['use_lang']
        self.backbone_name = args_override['backbone']
        self.lr_backbone = args_override['lr_backbone']
        self.image_size_h=int(args_override['image_size_h'])/32#type=int,default=480)
        self.image_size_w=int(args_override['image_size_w'])/32,#type=int,default=640)
        print(type(self.image_size_h))
        self.image_size_h=int(np.ceil(self.image_size_h))
        print(self.image_size_h)
        self.image_size_w=int(np.ceil(self.image_size_w))
        print(self.image_size_w)
        
        backbones = []
        pools = []
        linears = []
        for _ in self.camera_names:
            if self.backbone_name == 'resnet50_film':
                backbone = build_film_backbone(args_override)
                backbones.append(backbone)
            else:
                backbone = build_backbone(args_override)
                backbones.append(backbone)

            if self.pool_class == 'SpatialSoftmax':
                if self.backbone_name == 'resnet50':
                    input_shape = [2048, self.image_size_h,self.image_size_w]


                
                pools.append(
                    nn.Sequential(
                        SpatialSoftmax(**{'input_shape': input_shape, 'num_kp': self.stsm_num_kp, 'temperature': 1.0, 'learnable_temperature': False, 'noise_std': 0.0}),
                        nn.Flatten(start_dim=1, end_dim=-1)
                        )
                )
                linears.append(
                    nn.Sequential(
                        nn.Linear(int(np.prod([self.stsm_num_kp, 2])), self.stsm_num_kp),
                        nn.ReLU(),
                        nn.Linear(self.stsm_num_kp, self.img_fea_dim)
                    )
                )
            elif self.pool_class == 'null':
                pools.append(
                    nn.Sequential(
                        nn.Conv2d(backbones[0].num_channels, 1024, kernel_size=3, padding='same'),
                        #nn.LayerNorm(1024),
                        nn.ReLU(),
                        nn.Conv2d(1024, 256, kernel_size=3, padding='same'),
                        #nn.LayerNorm(256),
                        nn.ReLU(),
                        nn.Conv2d(256, 64, kernel_size=3, padding='same'),
                        #nn.LayerNorm(64),
                        nn.ReLU(),
                        nn.Conv2d(64, 8, kernel_size=1),
                        nn.Flatten(start_dim=1, end_dim=-1)
                    )
                )
                image_flatten_dim=8*self.image_size_h*self.image_size_w
                linears.append(torch.nn.Linear(image_flatten_dim, self.img_fea_dim))  

        backbones = nn.ModuleList(backbones)
        pools = nn.ModuleList(pools)
        linears = nn.ModuleList(linears)


        if self.use_filmfusion:
            self.obs_input_dim_film = self.img_fea_dim * len(self.camera_names)
            self.film_fusion = FiLM(self.obs_input_dim_film, 768)
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim
        else:
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim

        self.cond_obs_dim = args_override['cond_obs_dim'] # 512
        self.combine = nn.Sequential(
                nn.Linear(self.obs_input_dim, 1024),
                nn.ReLU(),
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Linear(512, self.cond_obs_dim)
            )
        if "resnet" in self.backbone_name:
            backbones = replace_bn_with_gn(backbones) # TODO


        print(f'Build noise_pred_net!')
        print(f"noise_pred_net cond obs dim: {self.cond_obs_dim}")

        noise_pred_net = ConditionalUnet1D(
            input_dim=self.ac_dim,
            global_cond_dim=self.cond_obs_dim
        )

        if self.use_filmfusion:
            self.nets = nn.ModuleDict({
                'policy': nn.ModuleDict({
                    'backbones': backbones,
                    'pools': pools,
                    'linears': linears,
                    'combine': self.combine,
                    'film_fusion': self.film_fusion,
                    'noise_pred_net': noise_pred_net
                })
            })
        else:
            self.nets = nn.ModuleDict({
                'policy': nn.ModuleDict({
                    'backbones': backbones,
                    'pools': pools,
                    'linears': linears,
                    'combine': self.combine,
                    'noise_pred_net': noise_pred_net
                })
            })
    
        #nets = nets.float().cuda()
        # ENABLE_EMA = False
        ENABLE_EMA = True
        if ENABLE_EMA:
            ema = EMAModel(model=self.nets.cuda(), power=self.ema_power)#.to(device=rank)
            #ema  = ema.to(rank)
            #ema  = DDP(ema , device_ids=[rank])
        else:
            ema = None

        self.ema = ema


        self.noise_scheduler = DDIMScheduler(
           num_train_timesteps=100,
           beta_schedule='squaredcos_cap_v2',
           clip_sample=True,
           set_alpha_to_one=True,
           steps_offset=0,
           prediction_type='epsilon'
        )

        n_parameters = sum(p.numel() for p in self.parameters())
        print("droid_diffusion number of parameters: %.2fM" % (n_parameters/1e6,))


    def configure_optimizers(self):
        # optimizer = torch.optim.AdamW(self.nets.parameters(), lr=self.lr, weight_decay=self.weight_decay)
        param_dicts = [
            {"params": [p for n, p in self.nets.named_parameters() if "backbone" not in n and p.requires_grad]},
            {
                "params": [p for n, p in self.nets.named_parameters() if "backbone" in n and p.requires_grad],
                "lr": self.lr_backbone,
            },
        ]
        optimizer = torch.optim.AdamW(param_dicts, lr=self.lr,
                                    weight_decay=self.weight_decay)

        return optimizer


    def __call__(self, qpos, image, actions=None, is_pad=None, language_distilbert=None,step=1):
        lang_embed = language_distilbert
        B = qpos.shape[0]
        normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                         std=[0.229, 0.224, 0.225])
        # language_distilbert: [1, 768]
        if self.norm:
            image = normalize(image)
        if actions is not None: # training time

            is_pad = is_pad[:, :self.num_queries]

            all_cam_features = []
            all_cam_pos = []
            for cam_id, cam_name in enumerate(self.camera_names):
                if self.use_film and lang_embed is not None:
                    cur_img = image[:, cam_id]

                    if self.color_randomizer is not None:
                        cur_img = self.color_randomizer._forward_in(cur_img)

                    language_embed=lang_embed
                    features,  = self.nets['policy']['backbones'][cam_id](cur_img, language_embed)
                else:
                    cur_img = image[:, cam_id]

                    if self.color_randomizer is not None:
                        cur_img = self.color_randomizer._forward_in(cur_img)
                        # print(f"4. cur_img: {cur_img.size()}")
                    features = self.nets['policy']['backbones'][cam_id](cur_img)

                pool_features = self.nets['policy']['pools'][cam_id](features)

                out_features = self.nets['policy']['linears'][cam_id](pool_features)


                all_cam_features.append(out_features)

            if self.use_filmfusion:
                all_cam_features = torch.cat(all_cam_features, dim=1)
                identity = all_cam_features
                all_cam_features = self.nets['policy']['film_fusion'](all_cam_features, lang_embed)
                obs_cond = all_cam_features + identity
                obs_cond = torch.cat([obs_cond, qpos], dim=1)
            else:
                obs_cond = torch.cat(all_cam_features + [qpos], dim=1)

            obs_cond = self.nets['policy']['combine'](obs_cond)

            noise = torch.randn([self.num_noise_samples] + list(actions.shape), device=obs_cond.device)

            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps, 
                (B,), device=obs_cond.device
            ).long()
            

            timesteps, noise = timesteps.to(obs_cond.device), noise.to(obs_cond.device)

            noisy_actions = torch.cat([self.noise_scheduler.add_noise(
                            actions, noise[i], timesteps)
                            for i in range(len(noise))], dim=0)
            
            obs_cond = obs_cond.repeat(self.num_noise_samples, 1)
            timesteps = timesteps.repeat(self.num_noise_samples)
            is_pad = is_pad.repeat(self.num_noise_samples, 1)

            noise_pred = self.nets['policy']['noise_pred_net'](noisy_actions, timesteps, global_cond=obs_cond)
            
            # L2 loss
            noise = noise.view(noise.size(0) * noise.size(1), *noise.size()[2:])

            
            all_l2 = F.mse_loss(noise_pred, noise, reduction='none')

            loss = (all_l2 * ~is_pad.unsqueeze(-1)).mean()

            loss_dict = {}
            loss_dict['l2_loss'] = loss
            loss_dict['loss'] = loss

            if self.training and self.ema is not None:
                self.ema.step(self.nets)

            return loss_dict
        else: # inference time
            To = self.observation_horizon
            Tp = self.prediction_horizon
            action_dim = self.ac_dim
            
            nets = self.nets
            if self.ema is not None:
                nets = self.ema.averaged_model
            
            all_cam_features = []
            for cam_id, cam_name in enumerate(self.camera_names):
                if self.use_film and lang_embed is not None:
                    features= nets['policy']['backbones'][cam_id](image[:, cam_id], lang_embed)
                else:
                    features = nets['policy']['backbones'][cam_id](image[:, cam_id])

                cam_features = features
                pool_features = nets['policy']['pools'][cam_id](cam_features)

                out_features = nets['policy']['linears'][cam_id](pool_features)
                all_cam_features.append(out_features)


            if self.use_filmfusion:
                all_cam_features = torch.cat(all_cam_features, dim=1)
                identity = all_cam_features
                all_cam_features = nets['policy']['film_fusion'](all_cam_features, lang_embed)
                obs_cond = all_cam_features + identity
                obs_cond = torch.cat([obs_cond, qpos], dim=1)
            else:
                obs_cond = torch.cat(all_cam_features + [qpos], dim=1)
            obs_cond =nets['policy']['combine'](obs_cond)

            # initialize action from Guassian noise
            noisy_action = torch.randn(
                (B, Tp, action_dim), device=obs_cond.device)
            naction = noisy_action
            
            # init scheduler
            self.noise_scheduler.set_timesteps(self.num_inference_timesteps)

            for k in self.noise_scheduler.timesteps:
                # predict noise
                noise_pred = nets['policy']['noise_pred_net'](
                    sample=naction, 
                    timestep=k,
                    global_cond=obs_cond
                )

                # inverse diffusion step (remove noise)
                naction = self.noise_scheduler.step(
                    model_output=noise_pred,
                    timestep=k,
                    sample=naction
                ).prev_sample

            return naction

    def serialize(self):
        return {
            "nets": self.nets.state_dict(),
            "ema": self.ema.averaged_model.state_dict() if self.ema is not None else None,
        }

 
    def deserialize(self, model_dict,replace_pool=False,steps=0):

        if  replace_pool:
            nets=model_dict["nets"]
            self_net=self.nets.state_dict()
            for key,value in self_net.items():
                if "pools" not in key:
                    self_net[key]=nets[key]
                elif "pools" in key:
                    print(f"don't replace{key}")
            status=self.nets.load_state_dict(self_net)
            if model_dict.get("ema", None) is not None:
                print('Loaded EMA')
                ema_net=model_dict["ema"]
                self_net_ema = self.ema.averaged_model.state_dict()
                for key, value in self_net_ema.items():
                    if "pools" not in key:
                        self_net_ema[key] = ema_net[key]
                    elif "pools" in key:
                        print(f"don't replace{key}")

                status_ema = self.ema.averaged_model.load_state_dict(self_net_ema)
                self.ema.optimization_step=steps
                status = [status, status_ema]
        else:

            status = self.nets.load_state_dict(model_dict["nets"])
            print('Loaded model')

            if model_dict.get("ema", None) is not None:
                print('Loaded EMA')
                status_ema = self.ema.averaged_model.load_state_dict(model_dict["ema"])
                self.ema.optimization_step = steps
                status = [status, status_ema]
        return status
