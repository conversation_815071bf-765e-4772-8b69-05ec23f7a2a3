from collections import deque

import torch
import numpy as np
import os
import pickle
import argparse
import matplotlib.pyplot as plt
from copy import deepcopy
from itertools import repeat
from tqdm import tqdm
from einops import rearrange
import wandb
import time
from torchvision import transforms


import cv2

import sys

from policy_model.droid_dit import DroidDiTPolicy

os.environ['DEVICE'] = "cuda"

FPS = 50
from utils.utils_ddp import  set_seed

from policy_model.droid_diffusion import DroidDiffusionPolicy

from transformers import AutoTokenizer, AutoModel



tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/ljm/distilbert-base-uncased')
lang_model= AutoModel.from_pretrained("/home/<USER>/ljm/distilbert-base-uncased", torch_dtype=torch.float16)
lang_model.to('cuda')

def get_auto_index(dataset_dir):
    max_idx = 1000
    for i in range(max_idx + 1):
        if not os.path.isfile(os.path.join(dataset_dir, f'qpos_{i}.npy')):
            return i
    raise Exception(f"Error getting auto index, or more than {max_idx} episodes")


# def main(args):
def prepare_config(ckpt_dir, policy_class, task_name,model_size,chunk_size,query_frequency,raw_lan,ckpt_names,use_data_aug=False,state_dim=14,action_dim=14):
    set_seed(1)
    # command line parameters
    is_eval = True
    validate_every = 1000000
    save_every = 1000000
    resume_ckpt_path = None
    backbone = 'resnet50'
    use_lang = True
    use_ddp = True
    # for droid diffusion
    lr_backbone = 1e-4
    pool_class = 'SpatialSoftmax'
    stsm_num_kp = 512
    img_fea_dim = 512
    cond_obs_dim = 512
    num_noise_samples = 8
    use_color_rand = False
    # get task parameters
    from aloha_scripts.constants import TASK_CONFIGS
    task_config = TASK_CONFIGS[task_name]

    # num_episodes = task_config['num_episodes']
    episode_len = task_config['episode_len']
    camera_names = task_config['camera_names']

    # fixed parameters
    print("state_dim>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", state_dim)

    policy_config = {'lr': lr_backbone,
                     'camera_names': camera_names,
                     'state_dim': state_dim,
                     'action_dim': action_dim,
                     'observation_horizon': 1,
                     'prediction_horizon': chunk_size,
                     'num_queries': query_frequency,
                     'num_inference_timesteps': 10,
                     'ema_power': 0.75,
                     'use_lang': use_lang,
                     'backbone': backbone,
                     'lr_backbone': lr_backbone,
                     'weight_decay': 0.0,
                     'pool_class': pool_class,
                     'stsm_num_kp': stsm_num_kp,
                     'img_fea_dim': img_fea_dim,
                     'cond_obs_dim': cond_obs_dim,
                     'num_noise_samples': num_noise_samples,
                     'use_color_rand': use_color_rand,
                     'double_arm': 1,
                     'image_size_w': 320,  # type=int,default=640)
                     'image_size_h': 240,  # type=int,default=480)
                     'use_filmfusion': 1,
                     'use_resnet_film': 0,

                     'ema_update_frequency': 1,
                     'ema_update_after_step': 1,
                     'use_data_aug': use_data_aug,
                     'query_frequency': query_frequency,
                     }
    if policy_class == 'DroidDiffusion':
        pass  # do not add other configs
    elif policy_class == 'DroidDiT':
        # have aug data in dataset!
        use_color_rand = False
        policy_config["model_size"] = model_size
        policy_config["use_color_rand"] = use_color_rand
    else:
        raise NotImplementedError

    config = {

        'validate_every': validate_every,
        'save_every': save_every,
        'ckpt_dir': ckpt_dir,
        'episode_len': episode_len,
        'state_dim': state_dim,
        'lr': lr_backbone,
        'policy_class': policy_class,
        'policy_config': policy_config,
        'task_name': task_name,
        'seed': 0,
        'camera_names': camera_names,
        'action_dim': action_dim,
        'load_pretrain': False,
        'use_lang': use_lang,
        'use_ddp': use_ddp,
        'use_base': False,
        'use_resnet_film': False,
        "raw_lan": raw_lan,
        "ckpt_names": ckpt_names,
        'use_data_aug': use_data_aug,
    }
    return config





def make_policy(policy_class, policy_config):
    if policy_class == 'DroidDiffusion':
        policy = DroidDiffusionPolicy(policy_config)
    elif policy_class == "DroidDiT":
        policy = DroidDiTPolicy(policy_config)
    else:
        raise NotImplementedError
    return policy


def pre_process(robot_state_value, key, stats):
    tmp = robot_state_value
    print("tmp", tmp.shape)
    print("key:", key)
    print(stats[key + '_mean'].shape)
    print(stats[key + '_std'].shape)
    tmp = (tmp - stats[key + '_mean']) / stats[key + '_std']
    return tmp


def get_obs(deplot_env_obs, stats, depth=False, use_base=False):
    cur_traj_data = dict()
    # (480, 270, 4)
    print(deplot_env_obs['images'].keys())
    cur_front_rgb = deplot_env_obs['images']['cam_front']  # camera_extrinsics image
    cur_top_rgb = deplot_env_obs['images']['cam_top']  # camera_extrinsics image
    cur_left_rgb = deplot_env_obs['images']['cam_left_wrist']  # camera_extrinsics image
    cur_right_rgb = deplot_env_obs['images']['cam_right_wrist']  # camera_extrinsics image

    cur_front_rgb = cv2.resize(cv2.cvtColor(cur_front_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_top_rgb = cv2.resize(cv2.cvtColor(cur_top_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_left_rgb = cv2.resize(cv2.cvtColor(cur_left_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_right_rgb = cv2.resize(cv2.cvtColor(cur_right_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]

    cv2.imshow("image", np.hstack([cur_right_rgb[..., ::-1], cur_left_rgb[..., ::-1], cur_top_rgb[..., ::-1]]))
    cv2.waitKey(1)
    cur_joint_positions = deplot_env_obs['qpos']



    print("curren joints:", cur_joint_positions.shape)
    cur_state = pre_process(cur_joint_positions, 'qpos', stats)


    cur_state = np.expand_dims(cur_state, axis=0)


    traj_rgb_np = np.array([cur_front_rgb,cur_top_rgb, cur_left_rgb, cur_right_rgb])
    traj_rgb_np = np.expand_dims(traj_rgb_np, axis=1)
    traj_rgb_np = np.transpose(traj_rgb_np, (1, 0, 4, 2, 3))

    return cur_state, traj_rgb_np


def time_ms():
    return time.time_ns() // 1_000_000


def eval_bc(config,  deploy_env, num_rollouts=1):
    set_seed(1000)
    ckpt_dir = config['ckpt_dir']
    policy_class = config['policy_class']
    policy_config = config['policy_config']

    if config['use_data_aug']:
        rand_crop_resize = True
    else:
        rand_crop_resize = False
    # rand_crop_resize=(config['policy_class'] == 'Diffusion')
    use_lang = config['use_lang']
    use_ddp = config['use_ddp']
    ckpt_name = config['ckpt_names']

    if use_lang:
        raw_lang = config["raw_lan"]

        encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
        outputs = lang_model(**encoded_input)
        encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
        # [1, 768]rerwer
        print(f'encoded_lang size: {encoded_lang.size()}')
        encoded_lang = encoded_lang.float()
    else:
        encoded_lang = None

    # load policy and stats
    ckpt_path = os.path.join(ckpt_dir, ckpt_name)
    policy = make_policy(policy_class, policy_config)
    if use_ddp:
        print("load Parameters from ddp ckpts!!!!!!", ckpt_path)
        checkpoint = torch.load(ckpt_path, map_location=torch.device('cpu'))
        print(checkpoint.keys())
        if "nets" in checkpoint.keys():
            loading_status = policy.deserialize(checkpoint['nets'], inference=True)
        else:
            loading_status = policy.deserialize(checkpoint, inference=True)
    else:
        loading_status = policy.deserialize(torch.load(ckpt_path))
    print(loading_status)
    policy.cuda()

    policy.eval()


    print(f'Loaded: {ckpt_path}')
    stats_path = os.path.join(ckpt_dir, f'dataset_stats.pkl')
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)

    # pre_process = lambda s_qpos: (s_qpos - stats['qpos_mean']) / stats['qpos_std']
    if policy_class == 'DroidDiffusion' or policy_class == "DroidDiT":
        post_process = lambda a: ((a + 1) / 2) * (stats['action_max'] - stats['action_min']) + stats['action_min']

    else:
        raise NotImplementedError
        


    query_frequency = policy_config['query_frequency']


    action_queue = deque(maxlen=query_frequency)
    max_timesteps = 10000000
    max_timesteps = int(max_timesteps * 1)  # may increase for real-world tasks



    for rollout_id in range(num_rollouts):
        rollout_id += 0
        print(f"env has reset!")
        image_list = []  # for visualization

        with torch.inference_mode():
            time0 = time.time()
            DT = 1 / FPS
            culmulated_delay = 0
            for t in range(max_timesteps):
                print(f"=============={t}")
                if t % 1500 == 1:
                    a = input("q means next eval:")
                    if a == 'q':
                        deploy_env.reset()
                        lang_in = input("Input the raw_lang(q means using default):")
                        if lang_in != 'q':
                            raw_lang = lang_in
                            encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
                            outputs = lang_model(**encoded_input)
                            encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
                            # [1, 768]
                            print(f'encoded_lang size: {encoded_lang.size()}')
                            encoded_lang = encoded_lang.float()

                time1 = time.time()
                obs = deploy_env.get_obs()

                # have processed robot_state
                robot_state, traj_rgb_np = get_obs(obs, stats)
                print("ok")
                image_list.append(traj_rgb_np)
                robot_state = torch.from_numpy(robot_state).float().cuda()

                if t % query_frequency == 0:
                    curr_image = torch.from_numpy(traj_rgb_np / 255.0).float().cuda()
                    if rand_crop_resize:
                        print('rand crop resize is used!')
                        original_size = curr_image.shape[-2:]
                        ratio = 0.95
                        curr_image = curr_image[...,
                                     int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                                     int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
                        curr_image = curr_image.squeeze(0)
                        resize_transform = transforms.Resize(original_size, antialias=True)
                        curr_image = resize_transform(curr_image)
                        curr_image = curr_image.unsqueeze(0)

                # warm up
                if t == 0:
                    for _ in range(2):
                        policy(robot_state, curr_image, language_distilbert=encoded_lang)
                    print('network warm up done')
                    time1 = time.time()

                ### query policy
                if config['policy_class'] == "Diffusion":
                    if t % query_frequency == 0:
                        all_actions = policy(robot_state, curr_image, language_distilbert=encoded_lang)
                    raw_action = all_actions[:, t % query_frequency]
                elif config['policy_class'] == "DroidDiffusion" or config['policy_class'] == "DroidDiT" :
                    if t % query_frequency == 0:
                        all_actions = policy(robot_state, curr_image, language_distilbert=encoded_lang).squeeze()[
                                      :query_frequency]
                        print(f"=======all_actions size: {all_actions.size()}")
                        action_queue.extend(all_actions)

                    raw_action = action_queue.popleft()

                else:
                    raise NotImplementedError

                print(f"raw action size: {raw_action.size()}")
                ### post-process actions
                raw_action = raw_action.squeeze(0).cpu().to(dtype=torch.float32).numpy()
                action = post_process(raw_action)
                print(f"after post_process action size: {action.shape}")

                ### step the environment
                print(f'step {t}, pred action: {action}')
                # input("Please enter to contue")
                # action_info = deploy_env.step(action, mode="absolute")
                action_info =  deploy_env.step(action)
                ### for visualization
                duration = time.time() - time1
                sleep_time = max(0, DT - duration)
                time.sleep(sleep_time)
                if duration >= DT:
                    culmulated_delay += (duration - DT)
                    print(
                        f'Warning: step duration: {duration:.3f} s at step {t} longer than DT: {DT} s, culmulated delay: {culmulated_delay:.3f} s')

            print(f'Avg fps {max_timesteps / (time.time() - time0)}')
            plt.close()


    return

class AgilexRobot():
    """Fake robot environment used for testing model evaluation, please replace this to your real environment."""
    def __init__(self):
        pass

    def step(self, action):
        print("Execute action successfully!!!")

    def reset(self):
        print("Reset to home position.")

    def get_obs(self):
        img = np.zeros((480, 640, 3)).astype(np.float32)
        obs = {
            "images": {
                'cam_front': img,
                'cam_top': img,
                'cam_left_wrist': img,
                'cam_right_wrist': img,
            },
            "qpos": np.zeros(14)
        }
        return obs

def init_robot():
    return AgilexRobot()

if __name__ == '__main__':
    
    #### 1. Initialize config ################
    ckpt_dir="/path/to/trained/ckpt/dir"
    policy_class="DroidDiT"
    task_name="example_use"
    model_size="DiT-H" # model size
    chunk_size=50 # prediction horizon
    query_frequency=16 # query frequency
    raw_lan='Fold t-shirt on the table.'
    ckpt_names="policy_step_4000_2025-01-05_15-37-27_compressed.ckpt"
    use_data_aug=True
    state_dim=14 # robot action dim
    action_dim=14 # robot state dim

    config = prepare_config(ckpt_dir=ckpt_dir,
                            policy_class=policy_class,
                            task_name=task_name,
                            model_size=model_size,
                            chunk_size=chunk_size,
                            query_frequency=query_frequency,
                            raw_lan=raw_lan,
                            ckpt_names=ckpt_names,
                            use_data_aug=use_data_aug,
                            state_dim=state_dim,
                            action_dim=action_dim)
    
    #### 2. Initialize robot env(Required)############
    deploy_env = init_robot()
    deploy_env.reset()
    #### 3. start evaluation ################

    eval_bc(config, deploy_env , num_rollouts=1)