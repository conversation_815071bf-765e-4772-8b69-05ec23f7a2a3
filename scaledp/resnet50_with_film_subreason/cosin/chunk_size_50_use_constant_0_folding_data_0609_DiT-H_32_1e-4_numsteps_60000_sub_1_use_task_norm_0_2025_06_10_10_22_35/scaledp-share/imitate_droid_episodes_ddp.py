import json

import pickle
import argparse
import warnings

from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
from aloha_scripts.constants import TASK_CONFIGS


from utils.utils_ddp import load_data, set_seed  # data functions
from policy_model.droid_dit import DroidDiTPolicy
from policy_model.droid_diffusion import DroidDiffusionPolicy
from datetime import datetime

import torch
import os
from torch.optim.lr_scheduler import CosineAnnealingLR
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
import torch.multiprocessing as mp

os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

os.environ['DEVICE'] = "cuda"

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Suppress specific future warnings from a library
warnings.filterwarnings('ignore', category=FutureWarning)


def get_auto_index(dataset_dir):
    max_idx = 1000
    for i in range(max_idx + 1):
        if not os.path.isfile(os.path.join(dataset_dir, f'qpos_{i}.npy')):
            return i
    raise Exception(f"Error getting auto index, or more than {max_idx} episodes")


def setup(rank, world_size, ddp_port):
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = ddp_port
    torch.cuda.set_device(rank)
    dist.init_process_group("nccl", rank=rank, world_size=world_size)


def train(rank, world_size, args):
    if torch.cuda.device_count() > 1:
        setup(rank, world_size, args['ddp_port'])

    set_seed(98797)
    # command line parameters
    is_eval = args['eval']
    ckpt_dir = args['ckpt_dir']
    policy_class = args['policy_class']
    onscreen_render = args['onscreen_render']
    task_name = args['task_name']
    batch_size_train = args['batch_size']
    batch_size_val = args['batch_size_val']
    num_steps = args['num_steps']
    eval_every = args['eval_every']
    validate_every = args['validate_every']
    save_every = args['save_every']
    resume = args['resume']
    print("resume >>>>>>>>>>>>>>>>",args['resume'])

    backbone = args['backbone']
    use_lang = args['use_lang']

    # for droid diffusion
    lr_backbone = args['lr_backbone']
    pool_class = args['pool_class']
    stsm_num_kp = args['stsm_num_kp']
    img_fea_dim = args['img_fea_dim']
    cond_obs_dim = args['cond_obs_dim']
    num_noise_samples = args['num_noise_samples']
    use_color_rand = args['use_color_rand']
    use_inspair_hand=args['use_inspair_hand']

    task_config = TASK_CONFIGS[task_name]
    dataset_dir = task_config['dataset_dir']
    episode_len = task_config['episode_len']
    camera_names = task_config['camera_names']
    stats_dir = task_config.get('stats_dir', None)
    sample_weights = task_config.get('sample_weights', None)
    train_ratio = task_config.get('train_ratio', 0.9999)
    name_filter = task_config.get('name_filter', lambda n: True)

    use_base = args["use_base"]

    # double arm for aloha, and single arm for franka
    state_dim = args['state_dim']
    action_dim = args['action_dim']  # 10 for aloha, 7 for franka

    # init policy config
    policy_config = {'lr': args['lr'],
                     'camera_names': camera_names,
                     'state_dim': state_dim,
                     'action_dim': action_dim,
                     'observation_horizon': 1,
                     'prediction_horizon': args['chunk_size'],
                     'num_queries': args['chunk_size'],
                     'num_inference_timesteps': 10,
                     'ema_power': 0.75,
                     'vq': False,
                     'use_lang': use_lang,
                     'backbone': backbone,
                     'weight_decay': 0.0,
                     'pool_class': pool_class,
                     'stsm_num_kp': stsm_num_kp,
                     'img_fea_dim': img_fea_dim,
                     'cond_obs_dim': cond_obs_dim,
                     'num_noise_samples': num_noise_samples,
                     'use_color_rand': use_color_rand,

                     'image_size_w': args['image_size_w'],  # type=int,default=640)
                     'image_size_h': args['image_size_h'],  # type=int,default=480)
                     'use_filmfusion': args['use_filmfusion'],
                     'lr_backbone':args['lr_backbone'],
                     'use_resnet_film':args['use_resnet_film'],
                     'use_moe':args['use_moe'],
                     'ema_update_frequency':args['ema_update_frequency'],
                     'ema_update_after_step':args['ema_update_after_step'],
                     }
    if policy_class == 'DroidDiffusion':
        pass  # do not add other configs
    elif policy_class == 'DroidDiT':
        # have aug data in dataset!
        use_color_rand = False
        policy_config["model_size"] = args['model_size']
        policy_config["use_color_rand"] = use_color_rand
    else:
        raise NotImplementedError

    actuator_config = {
        'actuator_network_dir': args['actuator_network_dir'],
        'history_len': args['history_len'],
        'future_len': args['future_len'],
        'prediction_len': args['prediction_len'],
    }

    config = {
        'num_steps': num_steps,
        'eval_every': eval_every,
        'validate_every': validate_every,
        'save_every': save_every,
        'ckpt_dir': ckpt_dir,
        'resume': resume,
        'episode_len': episode_len,
        'state_dim': state_dim,
        'lr': args['lr'],
        'policy_class': policy_class,
        'onscreen_render': onscreen_render,
        'policy_config': policy_config,
        'task_name': task_name,
        'seed': args['seed'],
        'camera_names': camera_names,
        'action_dim': action_dim,
        'load_pretrain': args['load_pretrain'],
        'actuator_config': actuator_config,
        'use_lang': use_lang,
        'use_resnet_film':args['use_resnet_film'],
        'ema_update_frequency':args['ema_update_frequency'],
    }

    if not os.path.isdir(ckpt_dir):
        os.makedirs(ckpt_dir, exist_ok=True)
    config_path = os.path.join(ckpt_dir, 'config.json')
    expr_name = ckpt_dir.split('/')[-1]

    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)

    train_dataloader,  stats = load_data(
        dataset_dir_l=dataset_dir,
        camera_names=camera_names,
        batch_size_train=batch_size_train,
        chunk_size=args['chunk_size'],
        skip_mirrored_data=args['skip_mirrored_data'],
        stats_dir_l=stats_dir,
        sample_weights=sample_weights,
        args=args,
        rank=rank)

    # save dataset stats
    stats_path = os.path.join(ckpt_dir, f'dataset_stats.pkl')
    with open(stats_path, 'wb') as f:
        pickle.dump(stats, f)
    print(">>>>>>>>>>>>>start training>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    train_bc(train_dataloader, config, rank,args=args)



def make_policy(policy_class, policy_config, rank=None):
    if policy_class == 'DroidDiffusion':
        policy = DroidDiffusionPolicy(policy_config)
    elif policy_class == 'DroidDiT':
        policy = DroidDiTPolicy(policy_config)
    else:
        raise NotImplementedError
    return policy


def make_optimizer(policy_class, policy):
    if policy_class == 'DroidDiffusion':
        if torch.cuda.device_count() > 1:
            optimizer = policy.module.configure_optimizers()
        else:
            optimizer = policy.configure_optimizers()
    elif policy_class == 'DroidDiT':
        if torch.cuda.device_count() > 1:
            optimizer = policy.module.configure_optimizers()
        else:
            optimizer = policy.configure_optimizers()
    else:
        raise NotImplementedError
    return optimizer


def forward_pass(data, policy, use_lang=False,step=0,rank=0):
    image_data, qpos_data, action_data, is_pad, lang_embed, dataset_path, episode_id = data
    image_data, qpos_data, action_data, is_pad= image_data.cuda(), qpos_data.cuda(), action_data.cuda(), is_pad.cuda()
    if not use_lang:
        lang_embed = None
    if lang_embed is not None:
        lang_embed = lang_embed.cuda()

    return policy(qpos_data, image_data, action_data, is_pad, language_distilbert=lang_embed,step=step )  # TODO remove None


def train_bc(train_dataloader, config, rank,args=None):
    num_steps = config['num_steps']
    ckpt_dir = config['ckpt_dir']
    policy_class = config['policy_class']
    policy_config = config['policy_config']
    save_every = config['save_every']
    use_lang = config['use_lang']
    writer = SummaryWriter(log_dir=os.path.join(ckpt_dir,"tensorboard_logs"))

    curr_step = 0
    policy = make_policy(policy_class, policy_config, rank=rank)
    if config['load_pretrain']:
        ckpt_filepath = args['load_pre_path']
        # loading_status = policy.deserialize(torch.load(ckpt_filepath), replace_pool=True)
        checkpoint = torch.load(ckpt_filepath,map_location=torch.device('cpu'))
        status = policy.deserialize(checkpoint['nets'],pre_ckpt=True)
        print(f">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>load pre train ckpt>>>>>>>>>>>>>>>>>>>{ckpt_filepath}")
        if rank == 0:
            print(f'loaded! {status}')

    if config['resume'] :
        print(config['resume'])
        print(">>>>>>>>>>>>>>>>>>>>>>>>start resmue Loading model>>>>>>>>>>>>>>>>>>>>>>>")
        file_list=os.listdir(ckpt_dir)
        curr_step=0
        for file in file_list:
            if file.endswith(".ckpt") and "compressed" not in file:
                step_number=int(file.split("step_")[1].split("_")[0])
                if step_number>curr_step:
                    curr_step=step_number
                    resume_ckpt_path = os.path.join(ckpt_dir, file)
        print(">>>>>>>>>>>>>>>>max resume step:",curr_step,">>>>>>>>>>>>>>>>>>>>>>>")

        checkpoint = torch.load(resume_ckpt_path,map_location=torch.device('cpu'))
        status = policy.deserialize(checkpoint['nets'],steps=checkpoint["step"]+1)
        curr_step = checkpoint["step"]+1
        optimizer_state=checkpoint["optimizer"]
        scheduler_state=checkpoint["scheduler"]
        print('Loaded model')
        loading_status = status

        print(f'curr_step: {curr_step}, num_steps: {num_steps}')
        print(f'Resume policy from: {config["resume"]}, Status: {loading_status}')

    if torch.cuda.device_count() > 1:
        policy = policy.to(rank)
        policy = DDP(policy, device_ids=[rank], find_unused_parameters=True)
    else:
        policy.cuda()

    optimizer = make_optimizer(policy_class, policy)
    scheduler = CosineAnnealingLR(optimizer, T_max=num_steps)
    if config['resume']:
        optimizer.load_state_dict(optimizer_state)
        scheduler.load_state_dict(scheduler_state)
        print(f'Resume optimizer from: {config["resume"]}')
        print(f'Resume scheduler from: {config["resume"]}')
        del optimizer_state
        del scheduler_state
        del checkpoint

    best_ckpt_info = None

    train_dataloader = repeater(train_dataloader, num_steps + 2, rank)
    loss = -1.0
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>start training>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    for step in tqdm(range(curr_step, num_steps + 1)):
        policy.train()
        optimizer.zero_grad()
        data = next(train_dataloader)
        forward_dict = forward_pass(data, policy, use_lang, step=step, rank=rank)

        loss = forward_dict['loss'].mean()
        loss.backward()


        #tensorboard log
        if rank == 0:
            if step % 500 == 0:
                writer.add_scalar("Loss/train", loss, global_step=step)
                writer.add_scalar("lr", optimizer.param_groups[0]["lr"], global_step=step)

        optimizer.step()
        if not args["use_constant"]:
            scheduler.step()


        if step == 0 or step % save_every == 0:
            if rank == 0:
                time_now = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                ckpt_path = os.path.join(ckpt_dir, f'policy_step_{step}_{time_now}.ckpt')
                if torch.cuda.device_count() > 1:
                    curr_ckpt_info = {"step": step + 1,
                                      "nets": policy.module.serialize(),
                                      "optimizer": optimizer.state_dict(),
                                      "scheduler": scheduler.state_dict(),
                                      "loss": loss}
                    torch.save(curr_ckpt_info, ckpt_path)
                else:
                    curr_ckpt_info = {"step": step + 1,
                                      "nets": policy.serialize(),
                                      "optimizer": optimizer.state_dict(),
                                      "scheduler": scheduler.state_dict(),
                                      "loss": loss}
                    torch.save(curr_ckpt_info, ckpt_path)




    return best_ckpt_info


def repeater(data_loader, total_steps, rank=0):
    step = 0
    while step < total_steps:
        # Create a new iterator for each epoch to ensure proper shuffling and distribution
        iterator = iter(data_loader)
        for data in iterator:
            yield data
            step += 1
            if step >= total_steps:
                break

        # Since the DataLoader is exhausted, synchronize all processes here
        if torch.distributed.is_initialized():
            torch.distributed.barrier()

        # Optionally, log the completion of an epoch on rank 0
        if rank == 0:
            print(f"Completed full pass through the DataLoader at step {step}")


def main(args):

    world_size = torch.cuda.device_count()
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--eval', action='store_true')
    parser.add_argument('--onscreen_render', action='store_true')
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt_dir', required=True)
    parser.add_argument('--policy_class', action='store', type=str, help='policy_class, capitalize', required=True)
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)
    parser.add_argument('--batch_size', action='store', type=int, help='batch_size', required=True)
    parser.add_argument('--seed', action='store', type=int, help='seed', required=True)
    parser.add_argument('--num_steps', action='store', type=int, help='num_steps', required=True)
    parser.add_argument('--lr', action='store', type=float, help='lr', required=True)
    parser.add_argument('--load_pretrain', action='store_true', default=False)
    parser.add_argument('--eval_every', action='store', type=int, default=500, help='eval_every', required=False)
    parser.add_argument('--validate_every', action='store', type=int, default=500, help='validate_every',
                        required=False)
    parser.add_argument('--save_every', action='store', type=int, default=500, help='save_every', required=False)

    parser.add_argument('--skip_mirrored_data', action='store_true')
    parser.add_argument('--actuator_network_dir', action='store', type=str, help='actuator_network_dir', required=False)
    parser.add_argument('--history_len', action='store', type=int)
    parser.add_argument('--future_len', action='store', type=int)
    parser.add_argument('--prediction_len', action='store', type=int)

    # for ACT
    parser.add_argument('--kl_weight', action='store', type=int, help='KL Weight', required=False)
    parser.add_argument('--chunk_size', action='store', type=int, help='chunk_size', required=False)
    parser.add_argument('--hidden_dim', action='store', type=int, help='hidden_dim', required=False)
    parser.add_argument('--dim_feedforward', action='store', type=int, help='dim_feedforward', required=False)
    parser.add_argument('--temporal_agg', action='store_true')
    parser.add_argument('--use_vq', action='store_true')
    parser.add_argument('--vq_class', action='store', type=int, help='vq_class')
    parser.add_argument('--vq_dim', action='store', type=int, help='vq_dim')
    parser.add_argument('--no_encoder', action='store_true')

    # efficientnet_b0film, efficientnet_b3film, efficientnet_b5film
    # resnet18, resnet34, resnet50
    # resnet18film, resnet34film, resnet50film need to be debuged
    parser.add_argument('--backbone', type=str, default='resnet18')
    parser.add_argument('--no_sepe_backbone', action='store_true')
    parser.add_argument('--use_lang', action='store_true')
    parser.add_argument('--input_state_acthead', action='store_true')
    parser.add_argument('--ddp_port', action='store', type=str, default='12355')

    # for droid_diffusion and ActDiffusionTransformer1,
    parser.add_argument('--lr_backbone', default=1e-4, type=float, help='lr_backbone')
    parser.add_argument('--pool_class', type=str, default='null')
    parser.add_argument('--stsm_num_kp', type=int, default=512)
    parser.add_argument('--img_fea_dim', type=int, default=512)
    parser.add_argument('--cond_obs_dim', type=int, default=512)
    parser.add_argument('--num_noise_samples', type=int, default=8)

    # for droid_diffusion
    # new version is always false, have aug data in dataset getitem
    parser.add_argument('--use_color_rand', action='store_true')
    parser.set_defaults(use_color_rand=False)

    # for ActDiffusionTransformer1
    parser.add_argument('--robostate_hidden_dim', type=int, default=128)
    parser.add_argument('--lang_hidden_dim', type=int, default=512)
    parser.add_argument('--encoder_hidden_dim', type=int, default=512)
    parser.add_argument('--pool_channels', type=int, default=32)
    parser.add_argument('--image_size_w', type=int, default=640)
    parser.add_argument('--image_size_h', type=int, default=480)
    parser.add_argument('--Resize_images', type=int, default=0)
    # for ActDiffusionTransformer2
    # robo_state_hidden_dim must equal to encoder_hidden_dim
    parser.add_argument('--trans_hidden_dim', type=int, default=512)
    parser.add_argument('--lang_raw', type=str, default=None)
    parser.add_argument('--pretrain_data', type=int, default=0)
    parser.add_argument('--batch_size_val', type=int, default=2)
    parser.add_argument('--bgr', type=int, default=0)
    parser.add_argument('--model_size', default="DiT-S", type=str)
    parser.add_argument('--is_use_sub_reason', default=0, type=int)
    parser.add_argument('--use_filmfusion', default=0, type=int)
    parser.add_argument('--use_base', default=0, type=int)
    parser.add_argument('--resume', type=int, help='resume train', default=0)
    parser.add_argument('--load_pre_path', type=str, help='load pretrain model path', default=None)
    parser.add_argument("--use_resnet_film",type=int,help='use resnet film 0 mean not use ',default=0)
    parser.add_argument("--use_moe", type=int, help='if or not use moe dit  ', default=0)
    parser.add_argument("--use_task_norm", type=int, help='if or not use task norm  action stuatus ', default=0)
    parser.add_argument("--not_use_soft_router", type=int, help='if or not use moe soft router', default=0)
    parser.add_argument("--use_constant", type=int, help='if or not use moe soft router', default=0)
    parser.add_argument("--use_inspair_hand", type=int, help='if or not inspair hand ', default=0)
    parser.add_argument("--only_use_base", type=int, help='only train base action  ', default=0)
    parser.add_argument("--ema_update_frequency", type=int, help='ema_update frequency  ', default=1)
    parser.add_argument("--ema_update_after_step", type=int, help='ema_update_after_step  ', default=0)
    parser.add_argument("--action_dim", type=int, help='action_dim', default=14)
    parser.add_argument("--state_dim", type=int, help='state_dim', default=14)
    main(vars(parser.parse_args()))
