#!/bin/bash
export OPENBLAS_NUM_THREADS=2
export GOTO_NUM_THREADS=2
export OMP_NUM_THREADS=2

backbone='resnet50'  #'efficientnet_b3film' #'filmresnet50' 'resnet50_film'
task_name=folding_data_0609 #11_1_reasoning_all_tasks #11_7_coa_new  #franka_multsk_12_task  franka_multsk_4_task
chunk_size=50
batch_size=32
policy_class=DroidDiT
model_size=DiT-H
# double_arm=1
lr=1e-4 #1e-4 ori 5e-5
lr_backbone=1e-4 #1e-4 ori 5e-5
pool_class='SpatialSoftmax'
seed=0
image_size_w=320 # image w  will resize to image_size_w
image_size_h=240 # image h  will resize to image_size_h
num_steps=60000
eval_every=800000
validate_every=1000000
save_every=10000
is_use_sub_reason=1 #1 mean use sub step reason, 0 mean not use sub step reason
use_filmfusion=1 #1 mean use last filmfusion
resume=0
use_resnet_film=0 #1 mean use resnet film, 0 mean use resnet 50 film come from mtact
use_task_norm=0
use_constant=0
action_dim=14
state_dim=14
filename_time=$(date "+%Y_%m_%d_%H_%M_%S")
code_path=/data/private/joy/scaledp-share


ckpt_dir=/data/private/joy/dex-checkpoints/stage1/${backbone}_with_film_subreason/cosine/chunk_size_${chunk_size}_use_constant_${use_constant}_${task_name}_${model_size}_${batch_size}_${lr}_numsteps_${num_steps}_sub_${is_use_sub_reason}_use_task_norm_${use_task_norm}

mkdir -p $ckpt_dir
cp -rn ${code_path} $ckpt_dir
cp -rn ${code_path}/utils $ckpt_dir
cp -rn ${code_path}/policy_model $ckpt_dir
cp -rn ${code_path}/imitate_droid_episodes_ddp.py $ckpt_dir
cp -rn ${code_path} $ckpt_dir

python3 get_norm_stats_by_task.py --task_name $task_name --ckpt_dir $ckpt_dir #calculate the norm stats for task

CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7, python3 imitate_droid_episodes_ddp.py \
--task_name $task_name \
--ckpt_dir $ckpt_dir --model_size ${model_size} \
--policy_class $policy_class --chunk_size $chunk_size --batch_size $batch_size \
--backbone $backbone \
--num_steps $num_steps --eval_every $eval_every --validate_every $validate_every --save_every $save_every  --lr $lr  --lr_backbone $lr_backbone \
--pool_class $pool_class  \
--seed $seed  --image_size_w $image_size_w  --image_size_h $image_size_h  --use_lang --is_use_sub_reason $is_use_sub_reason --use_filmfusion $use_filmfusion --resume $resume \
--use_resnet_film $use_resnet_film --use_task_norm $use_task_norm  --use_constant $use_constant \
--action_dim $action_dim --state_dim $state_dim \
2>&1 | tee $ckpt_dir/log.log
