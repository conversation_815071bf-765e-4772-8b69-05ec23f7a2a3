#!/bin/bash
export OPENBLAS_NUM_THREADS=2
export GOTO_NUM_THREADS=2
export OMP_NUM_THREADS=2

backbone='resnet50'  #'efficientnet_b3film' #'filmresnet50'
task_name=screwing_6_7 #11_1_reasoning_all_tasks #11_7_coa_new  #franka_multsk_12_task  franka_multsk_4_task
chunk_size=16
batch_size=128
policy_class=DroidDiT #DroidDiT #DroidDiffusion
model_size=DiT-L
lr=1e-4 #1e-4 ori 5e-5
lr_backbone=1e-4 #1e-4 ori 5e-5
pool_class='SpatialSoftmax'
seed=0
image_size_w=320
image_size_h=240
action_dim=10
state_dim=7
num_steps=100000
eval_every=800000
validate_every=1000000
save_every=10000
is_use_sub_reason=0
use_filmfusion=1
use_resnet_film=0 #1 mean use resnet film, 0 mean use resnet 50 film
resume=0
use_task_norm=0
filename_time=$(date "+%Y_%m_%d_%H_%M_%S")
code_path=/data/privare/ljm/code/scaledp-share
ckpt_dir=/data/team/ljm/model_param/scaledp/screwing/${task_name}_6_7
load_pre_path=/data/team/liuxiaoyu/checkpoint/scaledp/use_constant_0_mobile_franka_reasoning_w_vl_data_selected_version_2_DiT-L_320_240_128_1e-4_numsteps_40000_sub_0_2025_02_05_04_07_55/policy_step_40000_2025-02-05_23-56-08.ckpt
mkdir $ckpt_dir
cp -rn ${code_path}/train_scripts $ckpt_dir
cp -rn ${code_path}/utils $ckpt_dir
cp -rn ${code_path}/policy_model $ckpt_dir
cp -rn ${code_path}/imitate_droid_episodes_ddp.py $ckpt_dir
cp -rn ${code_path}/aloha_scripts $ckpt_dir

python3 get_norm_stats_by_task.py --task_name $task_name --ckpt_dir $ckpt_dir #calculate the norm stats for task
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python3 imitate_droid_episodes_ddp.py \
--task_name $task_name \
--ckpt_dir $ckpt_dir --model_size ${model_size} \
--policy_class $policy_class --chunk_size $chunk_size --batch_size $batch_size \
--backbone $backbone \
--num_steps $num_steps --eval_every $eval_every --validate_every $validate_every --save_every $save_every  --lr $lr  --lr_backbone $lr_backbone \
--pool_class $pool_class  \
--seed $seed  --image_size_w $image_size_w  --image_size_h $image_size_h   --use_lang --is_use_sub_reason $is_use_sub_reason --use_filmfusion $use_filmfusion --resume $resume \
--use_resnet_film $use_resnet_film --use_task_norm $use_task_norm \
--action_dim $action_dim --state_dim $state_dim \
--load_pre_path $load_pre_path  --load_pretrain \
  2>&1 | tee $ckpt_dir/log.log