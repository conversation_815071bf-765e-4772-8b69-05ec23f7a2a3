"""
Example usage of the modified DiT model with π0-style architecture.
This demonstrates how to use the model without AdaLN blocks and with joint attention.
"""

import torch
import torch.nn.functional as F
from policy_model.droid_dit import DiT, make_joint_attention_mask

def example_usage():
    """Example of how to use the modified π0-style DiT model."""
    
    # Model configuration
    batch_size = 4
    action_dim = 14  # robot action dimension
    cond_dim = 512   # condition feature dimension (e.g., image features)
    horizon = 16     # action sequence length
    cond_len = 8     # number of condition tokens
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Initialize the modified DiT model
    model = DiT(
        input_dim=action_dim,
        output_dim=action_dim,
        cond_dim=cond_dim,
        horizon=horizon,
        depth=12,           # number of transformer blocks
        n_emb=512,         # embedding dimension
        head_dim=64,       # attention head dimension
        num_heads=8,       # number of attention heads
        mlp_ratio=4.0,
        use_group_attention=False
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Prepare input data
    noisy_actions = torch.randn(batch_size, horizon, action_dim, device=device)
    timesteps = torch.rand(batch_size, device=device)  # Flow matching time [0, 1]
    condition_features = torch.randn(batch_size, cond_len, cond_dim, device=device)
    
    # Create position IDs for rotary embedding
    total_seq_len = cond_len + horizon
    position_ids = torch.arange(total_seq_len, device=device)[None, :].expand(batch_size, -1)
    
    # Create joint attention mask (π0 style)
    attn_mask = make_joint_attention_mask(
        cond_len=cond_len, 
        action_len=horizon, 
        device=device, 
        dtype=noisy_actions.dtype
    )
    
    # Forward pass
    with torch.no_grad():
        predicted_actions = model(
            x=noisy_actions,
            t=timesteps,
            global_cond=condition_features,
            position_ids=position_ids,
            attn_mask=attn_mask
        )
    
    print(f"Input shape: {noisy_actions.shape}")
    print(f"Output shape: {predicted_actions.shape}")
    print(f"Attention mask shape: {attn_mask.shape}")
    
    return model, predicted_actions

def training_example():
    """Example of training loop with the modified model."""
    
    # Model setup (same as above)
    model = DiT(
        input_dim=14, output_dim=14, cond_dim=512, horizon=16,
        depth=12, n_emb=512, head_dim=64, num_heads=8
    )
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # Training loop
    model.train()
    for step in range(10):  # Mini training loop
        batch_size = 4
        
        # Sample data
        clean_actions = torch.randn(batch_size, 16, 14)
        condition_features = torch.randn(batch_size, 8, 512)
        
        # Flow matching sampling (π0 style)
        t = torch.rand(batch_size)
        noise = torch.randn_like(clean_actions)
        
        # Linear interpolation: x_t = t * noise + (1 - t) * clean_actions
        t_expanded = t[:, None, None]
        noisy_actions = t_expanded * noise + (1 - t_expanded) * clean_actions
        
        # Velocity field target: u_t = noise - clean_actions
        target_velocity = noise - clean_actions
        
        # Position IDs and attention mask
        position_ids = torch.arange(24)[None, :].expand(batch_size, -1)  # 8 + 16
        attn_mask = make_joint_attention_mask(8, 16, clean_actions.device, clean_actions.dtype)
        
        # Forward pass
        predicted_velocity = model(
            x=noisy_actions,
            t=t,
            global_cond=condition_features,
            position_ids=position_ids,
            attn_mask=attn_mask
        )
        
        # Flow matching loss
        loss = F.mse_loss(predicted_velocity, target_velocity)
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        if step % 5 == 0:
            print(f"Step {step}, Loss: {loss.item():.4f}")

def compare_architectures():
    """Compare the key differences between original and π0-style architectures."""
    
    print("=== Architecture Comparison ===")
    print("\n1. Time Conditioning:")
    print("   Original: MLP(t) → AdaLN parameters → modulate each layer")
    print("   π0 Style: Sinusoidal(t) → concat with actions → token-level fusion")
    
    print("\n2. Attention Mechanism:")
    print("   Original: Standard self-attention with causal mask")
    print("   π0 Style: Joint attention with complex mask (condition ↔ action)")
    
    print("\n3. Layer Structure:")
    print("   Original: AdaLN + Attention + AdaLN + MLP")
    print("   π0 Style: LayerNorm + Attention + LayerNorm + MLP")
    
    print("\n4. Conditioning:")
    print("   Original: Global conditioning through AdaLN modulation")
    print("   π0 Style: Token-level conditioning through attention masks")

if __name__ == "__main__":
    print("Testing π0-style DiT model...")
    
    # Run examples
    model, output = example_usage()
    print("\n" + "="*50)
    
    training_example()
    print("\n" + "="*50)
    
    compare_architectures()
