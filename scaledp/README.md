Here’s a revised version of your passage with improved grammar, clarity, and structure:

---

# Diffusion Policy

This repository contains the implementation of **Diffusion Policy** and **Scaledp**, along with two simulated environments for training and evaluation.

---

## Updates

- **Latest updates**: Check the repository for the most recent changes and improvements.

---

## Repository Structure

The repository is organized as follows:

- **`imitate_droid_episodes_ddp.py`**: Script for training and evaluating Diffusion Policy (DP).
- **`policy_model`**: Contains implementations of the Diffusion Policy and Scaledp.
  - **`diffusion_policy.py`**: Implementation of the Diffusion Policy.
  - **`droid_dit.py`**: Implementation of the Scaledp model structure.
- **`utils/utils_ddp.py`**: Utility functions for training and data loading.
- **`aloha_scripts/constants.py`**: Defines task names, data save directories, and camera names.
- **`utils/add_language_to_h5.py)`**: add distilbert language embedding to h5 file.
---

## Installation

To set up the environment, follow these steps:

1. **Create a Conda environment**:
   ```bash
   conda create -n minidp python=3.10
   conda activate minidp
   ```

2. **Install PyTorch**:
   ```bash
   pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0
   ```

3. **Install additional dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

---

## Example Usages

### Setting Up a New Terminal

1. Activate the Conda environment:
   ```bash
   conda activate minidp
   ```

2. Modify the `scaledp_train.sh` script:
   - **`<policy_class>`**: Specify the model class. For Scaledp, use `DroidDiT`.
   - **`<model_size>`**: Choose the model size:
     - `DiT-B`: Base model (100M parameters).
     - `DiT-L`: Large model (410M parameters).
     - `DiT-H`: Huge model (1B parameters).
   - **`<code_path>`**: Path to the code directory.
   - **`<ckpt_dir>`**: Path to save checkpoints.
   - **`<task_name>`**: Name of the task.
   - **`<num_steps>`**: Number of training steps.
   - **`<use_filmfusion>`**: use Film layer fusion observation and distilbert language embedding. 1 means use, 0 means not use.

3. Run the training script:
   ```bash
   bash scaledp_train.sh
   ```

---

## Additional Notes

- Ensure that the paths in the scripts are correctly set to match your system configuration.
- For detailed training logs and evaluation metrics, check the output directories specified in the scripts.

---

## License

This project is licensed under the MIT License. See the `LICENSE` file for details.

---

For any questions or issues, please open an issue on the repository or contact the maintainers. Happy coding! 🚀

---

This version improves readability, fixes grammatical issues, and ensures consistency in formatting. Let me know if you need further adjustments!