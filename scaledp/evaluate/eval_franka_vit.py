import os
import pickle
import argparse
import time
import cv2
import sys
import torch
import numpy as np
import utils.torch_utils as TorchUtils

from collections import deque
from copy import deepcopy
from einops import rearrange
from PIL import Image
from torchvision import transforms
from transformers import AutoTokenizer, AutoModel, CLIPImageProcessor
from policy_model.droid_dit import DroidDiTPolicy
from policy_model.droid_diffusion import DroidDiffusionPolicy

import IPython

FPS = 30
e = IPython.embed

tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/zhumj/mllm_param/distilbert-base-uncased')
lang_model = AutoModel.from_pretrained("/home/<USER>/zhumj/mllm_param/distilbert-base-uncased",
                                       torch_dtype=torch.float16)
image_processor = CLIPImageProcessor.from_pretrained('/home/<USER>/zhumj/mllm_param/openai/clip-vit-large-patch14')
lang_model.to('cuda')

idx = 1


def set_seed(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)


def init_robot():
    sys.path.insert(0, "/home/<USER>/Dev-Code/droid")
    from droid.robot_env import RobotEnv

    policy_timestep_filtering_kwargs = {'action_space': 'cartesian_position', 'gripper_action_space': 'position',
                                        'robot_state_keys': ['cartesian_position', 'gripper_position',
                                                             'joint_positions']}
    # resolution (w, h)
    policy_camera_kwargs = {
        'hand_camera': {'image': True, 'concatenate_images': False, 'resolution': (640, 480), 'resize_func': 'cv2'},
        'varied_camera': {'image': True, 'concatenate_images': False, 'resolution': (640, 480), 'resize_func': 'cv2'}}

    deploy_env = RobotEnv(
        action_space=policy_timestep_filtering_kwargs["action_space"],
        gripper_action_space=policy_timestep_filtering_kwargs["gripper_action_space"],
        camera_kwargs=policy_camera_kwargs
    )
    deploy_env._robot.establish_connection()
    deploy_env.camera_reader.set_trajectory_mode()
    return deploy_env


def main(args):
    # <<<<<<<<<<<<<<<<<<<<<<<<<<< config setup >>>>>>>>>>>>>>>>>>>>>>>>>>
    set_seed(args["seed"])
    policy_class = args['policy_class']
    task_name = args['task_name']
    backbone = args['backbone']

    img_fea_dim = args['img_fea_dim']
    cond_obs_dim = args['cond_obs_dim']
    num_noise_samples = args['num_noise_samples']
    model_size = args["model_size"]

    ckpt_dir = args['ckpt_dir']
    ckpt_name = args['ckpt_name']
    raw_lang = args['raw_lang']
    from aloha_scripts.constants import TASK_CONFIGS
    task_config = TASK_CONFIGS[task_name]
    camera_names = task_config['camera_names']

    # fixed parameters
    state_dim = 7  # 14
    action_dim = 10

    policy_config = {'lr': args['lr'],
                     'lr_backbone':args['lr_backbone'],
                     'camera_names': camera_names,
                     'state_dim': state_dim,
                     'action_dim': action_dim,
                     'observation_horizon': 1,
                     'prediction_horizon': args['chunk_size'],
                     'num_queries': args['chunk_size'],
                     'num_inference_timesteps': 10,
                     'ema_power': 0.75,
                     'backbone': backbone,
                     'weight_decay': 0.0,
                     'img_fea_dim': img_fea_dim,
                     'cond_obs_dim': cond_obs_dim,
                     'num_noise_samples': num_noise_samples
                     }
    if policy_class == 'DroidDiffusion':
        pass
    elif policy_class == 'DroidDiT':
        policy_config["model_size"] = model_size
    else:
        raise NotImplementedError

    config = {
        'ckpt_dir': ckpt_dir,
        'state_dim': state_dim,
        'lr': args['lr'],
        'policy_class': policy_class,
        'policy_config': policy_config,
        'task_name': task_name,
        'seed': args['seed'],
        'camera_names': camera_names,
        'action_dim': action_dim,
        'load_pretrain': args['load_pretrain'],
        'num_noise_samples': num_noise_samples,
        'ckpt_name': ckpt_name,
        'raw_lang': raw_lang,
    }

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< init robot >>>>>>>>>>>>>>>>>>>>>>>>>>
    deploy_env = init_robot()

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< eval policy >>>>>>>>>>>>>>>>>>>>>>>>>>
    ckpt_names = [f'{ckpt_name}.ckpt']
    for ckpt_name in ckpt_names:
        eval_bc(config, ckpt_name, deploy_env, save_episode=True, num_rollouts=1)

    exit()


def make_policy(policy_class, policy_config, stats=None):
    if policy_class == 'DroidDiffusion':
        policy = DroidDiffusionPolicy(policy_config)
    elif policy_class == 'DroidDiT':
        policy = DroidDiTPolicy(policy_config)
    else:
        raise NotImplementedError
    return policy


def pre_process(robot_state_value, key, stats):
    tmp = robot_state_value
    tmp = (tmp - stats[key + '_mean']) / stats[key + '_std']
    return tmp


def get_obs(deplot_env_obs, stats):

    # >>>>>>>>>>>>>>>>> image resize <<<<<<<<<<<<<<<<<
    cur_right_rgb = deplot_env_obs['image']['21729895_left']   # 640 480
    cur_left_rgb = deplot_env_obs['image']['29392465_left']    # 640 480
    cur_wrist_rgb = deplot_env_obs['image']['12035220_left']  # camera_extrinsics image 1280 720 shape
    # cur_right_rgb = cv2.resize(cur_right_rgb, (640, 480))
    # cur_left_rgb = cv2.resize(cur_left_rgb, (640, 480))
    cur_wrist_rgb = cv2.resize(cur_wrist_rgb, (640, 480))
    # cur_right_rgb = cv2.resize(cur_right_rgb, (320, 240))
    # cur_left_rgb = cv2.resize(cur_left_rgb, (320, 240))
    # cur_wrist_rgb = cv2.resize(cur_wrist_rgb, (320, 240))

    w, h = 640, 480
    center = (w // 2, h // 2)
    angle = 180
    scale = 1.0
    M = cv2.getRotationMatrix2D(center, angle, scale)
    cur_wrist_rgb = cv2.warpAffine(cur_wrist_rgb, M, (w, h))
    cur_right_rgb = cv2.cvtColor(cur_right_rgb, cv2.COLOR_BGRA2BGR)[:, :, ::-1]
    cur_left_rgb = cv2.cvtColor(cur_left_rgb, cv2.COLOR_BGRA2BGR)[:, :, ::-1]
    cur_wrist_rgb = cv2.cvtColor(cur_wrist_rgb, cv2.COLOR_BGRA2BGR)[:, :, ::-1]
    # cv2.imshow('cur_rgb', cv2.hconcat([cv2.resize(cur_left_rgb, (224, 224)),
    #                                    cv2.resize(cur_right_rgb, (224, 224)),
    #                                    cv2.resize(cur_wrist_rgb, (224, 224))]))
    # cv2.waitKey(1)
    save_img(cur_left_rgb, cur_right_rgb, cur_wrist_rgb)

    # >>>>>>>>>>>>>>>>> state <<<<<<<<<<<<<<<<<
    cur_cartesian_position = np.array(deplot_env_obs['robot_state']['cartesian_position'])
    cur_gripper_position = np.expand_dims(np.array(deplot_env_obs['robot_state']['gripper_position']), axis=0)
    cur_state_np_raw = np.concatenate((cur_cartesian_position, cur_gripper_position))
    cur_state_np = pre_process(cur_state_np_raw, 'qpos', stats)
    cur_state_np = np.expand_dims(cur_state_np, axis=0)

    # >>>>>>>>>>>>>>>>> image crop and resize, similar to the train image preprocess <<<<<<<<<<<<<<<<<
    cur_left_rgb = np.array(cur_left_rgb)
    cur_right_rgb = np.array(cur_right_rgb)
    cur_wrist_rgb = np.array(cur_wrist_rgb)
    curr_images = np.array([cur_left_rgb, cur_right_rgb, cur_wrist_rgb])
    curr_images = torch.from_numpy(curr_images)
    curr_images = np.transpose(curr_images, (0, 3, 1, 2))
    original_size = curr_images.shape[-2:]
    ratio = 0.95
    curr_images = curr_images[...,
                 int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                 int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
    resize_transform = transforms.Resize(original_size, antialias=True)
    curr_images = resize_transform(curr_images)

    # >>>>>>>>>>>>>>>>> clip preprocess <<<<<<<<<<<<<<<<<
    traj_rgb = []
    for img in curr_images:
        img = image_processor.preprocess(img, return_tensors='pt')['pixel_values'][0]
        traj_rgb.append(img)

    traj_rgb = torch.stack(traj_rgb, dim=0)
    traj_rgb = traj_rgb[None, ...]  # add bs=1

    return cur_state_np_raw, cur_state_np, traj_rgb


def save_img(cur_left_rgb, cur_right_rgb, cur_wrist_rgb):
    global idx
    Image.fromarray(cur_left_rgb).save(f"/home/<USER>/zhumj/eval_episode_img/left/{idx}.jpg")
    Image.fromarray(cur_right_rgb).save(f"/home/<USER>/zhumj/eval_episode_img/right/{idx}.jpg")
    Image.fromarray(cur_wrist_rgb).save(f"/home/<USER>/zhumj/eval_episode_img/wrist/{idx}.jpg")
    idx += 1


def convert_actions(pred_action):
    cur_xyz = pred_action[:3]
    cur_rot6d = pred_action[3:9]
    cur_gripper = np.expand_dims(pred_action[-1], axis=0)

    cur_rot6d = torch.from_numpy(cur_rot6d).unsqueeze(0)
    cur_euler = TorchUtils.rot_6d_to_euler_angles(rot_6d=cur_rot6d, convention="XYZ").squeeze().numpy()
    pred_action = np.concatenate((cur_xyz, cur_euler, cur_gripper))
    print(f'4. after convert pred_action: {pred_action}')

    return pred_action


def eval_bc(config, ckpt_name, deploy_env, save_episode=True, num_rollouts=1):
    set_seed(1000)
    ckpt_dir = config['ckpt_dir']
    action_dim = config['action_dim']
    policy_class = config['policy_class']
    policy_config = config['policy_config']
    raw_lang = config['raw_lang']

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< encode lang >>>>>>>>>>>>>>>>>>>>>>>>>>
    print("raw lang: ", raw_lang)
    encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
    outputs = lang_model(**encoded_input)
    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
    print(f'encoded_lang size: {encoded_lang.size()}')
    encoded_lang = encoded_lang.float()

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< load policy >>>>>>>>>>>>>>>>>>>>>>>>>>
    ckpt_path = os.path.join(ckpt_dir, ckpt_name)
    policy = make_policy(policy_class, policy_config)
    print(policy)
    checkpoint = torch.load(ckpt_path, map_location=torch.device('cpu'))
    if "compressed" in ckpt_path:
        loading_status = policy.deserialize(checkpoint, inference=True)
    else:
        loading_status = policy.deserialize(checkpoint["nets"], inference=True)
    policy.cuda()
    policy.eval()
    print(f'Loaded: {ckpt_path}')
    stats_path = os.path.join(ckpt_dir, f'dataset_stats.pkl')
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< action post process >>>>>>>>>>>>>>>>>>>>>>>>>>
    if policy_class == "DroidDiT" or policy_class == "DroidDiffusion":
        print(f"============ DroidDiT post_process is called")
        post_process = lambda a: ((a + 1) / 2) * (stats['action_max'] - stats['action_min']) + stats['action_min']
    else:
        raise NotImplementedError
    env = deploy_env

    query_frequency = policy_config['num_queries']

    if policy_class == "DroidDiT":
        query_frequency = policy_config['num_queries'] // 1

    max_timesteps = 10000

    action_queue = deque(maxlen=query_frequency)
    for rollout_id in range(100000):
        env.reset(randomize=False)
        print(f"env has reset!")

        # <<<<<<<<<<<<<<<<<<<<<<<<<<< evaluation loop >>>>>>>>>>>>>>>>>>>>>>>>>>
        with torch.inference_mode():
            time0 = time.time()
            DT = 1 / FPS
            culmulated_delay = 0
            for t in range(max_timesteps):
                print(f"=============={t}")
                if t % 100 == 1:
                    a = input("q means next eval:")
                    if a == 'q':
                        env.reset(randomize=False)
                        lang_in = input("Input the raw_lang(q and enter mean using default):")
                        if lang_in != 'q' or lang_in != '\n':
                            raw_lang = lang_in
                            print(raw_lang)
                            print("@@" * 30)
                            encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
                            outputs = lang_model(**encoded_input)
                            encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0).float()
                            action_queue = deque(maxlen=query_frequency)
                        break
                    if a == 'c':
                        env.reset(randomize=False)
                        input("Input any key for continue test:")
                        print(raw_lang)
                        action_queue = deque(maxlen=query_frequency)
                        break

                # <<<<<<<<<<<<<<<<<<<<<<<<<<< get obs >>>>>>>>>>>>>>>>>>>>>>>>>>
                obs = deploy_env.get_observation()
                cur_state_np_raw, robot_state, traj_rgb = get_obs(obs, stats)
                robot_state = torch.from_numpy(robot_state).float().cuda()

                curr_image = traj_rgb.cuda()
                # <<<<<<<<<<<<<<<<<<<<<<<<<<< warm up >>>>>>>>>>>>>>>>>>>>>>>>>>
                if t == 0:
                    for _ in range(10):
                        policy(robot_state, curr_image, language_distilbert=encoded_lang)
                    print('network warm up done')

                # <<<<<<<<<<<<<<<<<<<<<<<<<<< query policy >>>>>>>>>>>>>>>>>>>>>>>>>>
                time1 = time.time()
                if config['policy_class'].find("Diffusion") >= 0 or config['policy_class'].find("DiT") >= 0:
                    if len(action_queue) == 0:
                        all_actions = policy(robot_state, curr_image, language_distilbert=encoded_lang)[:,
                                      0:0 + query_frequency]
                        print(f"======= t :{t}, all_actions size: {all_actions.size()}")
                        action_queue.extend(all_actions[0])
                    raw_action = action_queue.popleft()
                    print(f"t: {t}, raw_action: {raw_action.size()}")
                else:
                    raise NotImplementedError

                print(f"raw action size: {raw_action.size()}")
                raw_action = raw_action.squeeze(0).cpu().numpy()
                action = post_process(raw_action)
                print(f"after post_process action size: {action.shape}")

                action = convert_actions(action)
                ### step the environment
                ts = env.step(action)
                print(f'step {t}, pred action: {action}')

                duration = time.time() - time1
                sleep_time = max(0, DT - duration)
                time.sleep(sleep_time)
                if duration >= DT:
                    culmulated_delay += (duration - DT)
                    print(
                        f'Warning: step duration: {duration:.3f} s at step {t} longer than DT: {DT} s, culmulated delay: {culmulated_delay:.3f} s')

            print(f'Avg fps {max_timesteps / (time.time() - time0)}')
    return


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt dir used for saving traing ckpts',
                        required=True)
    parser.add_argument('--policy_class', action='store', type=str,
                        help='the policy class used for imitation learning, DroidDiT, capitalize', required=True)
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)
    parser.add_argument('--batch_size', action='store', type=int, help='batch_size', required=True)
    parser.add_argument('--seed', action='store', type=int, help='seed', required=True)
    parser.add_argument('--num_steps', action='store', type=int, help='number of training steps', required=True)
    parser.add_argument('--lr', action='store', type=float, help='lr', required=True)
    parser.add_argument('--lr_backbone', default=1e-4, type=float, help='lr_backbone')
    parser.add_argument('--save_every', action='store', type=int, default=500, help='save_every', required=False)

    ### model hyperparameters
    parser.add_argument('--num_noise_samples', type=int, default=8)
    parser.add_argument('--chunk_size', action='store', type=int, help='chunk_size', required=False)
    parser.add_argument('--backbone', type=str, default='resnet18')
    parser.add_argument('--img_fea_dim', type=int, default=512)
    parser.add_argument('--cond_obs_dim', type=int, default=512)
    parser.add_argument('--model_size', default="DiT-S", type=str)

    ### data hyperparameters
    parser.add_argument('--image_size_w', type=int, default=640)
    parser.add_argument('--image_size_h', type=int, default=480)
    parser.add_argument('--bgr', type=int, default=0)
    parser.add_argument('--use_color_rand', action='store_true', default=False)

    ### state hyperparameters (input dim and output dim setup)
    parser.add_argument('--double_arm', type=int, default=0)
    parser.add_argument('--is_use_sub_reason', default=0, type=int)
    parser.add_argument('--use_base', default=0, type=int)
    parser.add_argument("--use_inspair_hand", type=int, help='if or not inspair hand ', default=0)
    parser.add_argument("--only_use_base", type=int, help='only train base action  ', default=0)

    ### pretrain and resume hyperparameters
    parser.add_argument('--load_pretrain', action='store_true', default=False)
    parser.add_argument('--resume', type=int, help='resume train', default=0)
    parser.add_argument('--load_pre_path', type=str, help='load pretrain model path', default=None)
    parser.add_argument("--use_constant", type=int, help='for optimizer shelduer', default=0)

    ### for eval
    parser.add_argument('--ckpt_name', type=str, default='null')
    parser.add_argument('--raw_lang', type=str, default='null')
    main(vars(parser.parse_args()))
