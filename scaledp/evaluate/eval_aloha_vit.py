import json

import timm
import torch
import numpy as np
import os
import pickle
import argparse
import time
import cv2
import sys

from PIL import Image
from collections import deque
from torchvision import transforms
from transformers import AutoTokenizer, AutoModel, CLIPImageProcessor

from utils.utils_ddp import set_seed
from policy_model.droid_dit import DroidDiTPolicy

os.environ['DEVICE'] = "cuda"
FPS = 30

tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/Documents/zhumj/model_param/mllm/distilbert-base-uncased')
lang_model = AutoModel.from_pretrained("/home/<USER>/Documents/zhumj/model_param/mllm/distilbert-base-uncased", torch_dtype=torch.float16)
# image_processor = CLIPImageProcessor.from_pretrained('/home/<USER>/Documents/zhumj/model_param/mllm/openai/clip-vit-base-patch16')
with open(os.path.join('/home/<USER>/Documents/zhumj/model_param/mllm/timm/vit_base_patch16_rope_reg1_gap_256.sbb_in1k', "data_config.json"), 'r', encoding='utf-8') as f:
    data_config = json.load(f)
image_processor = timm.data.create_transform(**data_config, is_training=False)
lang_model.to('cuda')
idx = 1


def init_robot():
    sys.path.insert(0, "/home/<USER>/Dev-Code/mirocs")
    from run.agilex_robot_env import AgilexRobot
    agilex_bot = AgilexRobot()
    return agilex_bot


def main(args):
    # <<<<<<<<<<<<<<<<<<<<<<<<<<< config setup >>>>>>>>>>>>>>>>>>>>>>>>>>
    set_seed(args["seed"])
    policy_class = args['policy_class']
    task_name = args['task_name']
    backbone = args['backbone']

    img_fea_dim = args['img_fea_dim']
    cond_obs_dim = args['cond_obs_dim']
    num_noise_samples = args['num_noise_samples']
    model_size = args["model_size"]

    ckpt_dir = args['ckpt_dir']
    ckpt_name = args['ckpt_name']
    raw_lang = args['raw_lang']
    from aloha_scripts.constants import TASK_CONFIGS
    task_config = TASK_CONFIGS[task_name]
    camera_names = task_config['camera_names']

    # fixed parameters
    if args['use_base']:
        state_dim = 16  # 19 #19 for serl data #14
        action_dim = 16  # 7
    else:
        state_dim = 14  # 19 #19 for serl data #14
        action_dim = 14  # 7
    print("state_dim>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", state_dim)
    policy_config = {'lr': args['lr'],
                     'lr_backbone': args['lr_backbone'],
                     'camera_names': camera_names,
                     'state_dim': state_dim,
                     'action_dim': action_dim,
                     'observation_horizon': 1,
                     'prediction_horizon': args['chunk_size'],
                     'num_queries': args['chunk_size'],
                     'num_inference_timesteps': 10,
                     'ema_power': 0.75,
                     'backbone': backbone,
                     'weight_decay': 0.0,
                     'img_fea_dim': img_fea_dim,
                     'cond_obs_dim': cond_obs_dim,
                     'num_noise_samples': num_noise_samples,
                     "using_vit_film": args['using_vit_film'],
                     }
    if policy_class == 'DroidDiffusion':
        pass
    elif policy_class == 'DroidDiT':
        policy_config["model_size"] = model_size
    else:
        raise NotImplementedError

    config = {
        'ckpt_dir': ckpt_dir,
        'state_dim': state_dim,
        'lr': args['lr'],
        'policy_class': policy_class,
        'policy_config': policy_config,
        'task_name': task_name,
        'seed': args['seed'],
        'camera_names': camera_names,
        'action_dim': action_dim,
        'load_pretrain': args['load_pretrain'],
        'use_base': args['use_base'],
        'num_noise_samples': num_noise_samples,
        'ckpt_name': ckpt_name,
        'raw_lang': raw_lang,
    }

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< init robot >>>>>>>>>>>>>>>>>>>>>>>>>>
    agilex_bot = init_robot()

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< eval policy >>>>>>>>>>>>>>>>>>>>>>>>>>
    ckpt_names = [f'{ckpt_name}.ckpt']
    for ckpt_name in ckpt_names:
        eval_bc(config, ckpt_name, agilex_bot)

    exit()


def make_policy(policy_class, policy_config):
    if  policy_class == "DroidDiT":
        policy = DroidDiTPolicy(policy_config)
    else:
        raise NotImplementedError
    return policy


def pre_process(robot_state_value, key, stats):
    tmp = robot_state_value
    tmp = (tmp - stats[key + '_mean']) / stats[key + '_std']
    return tmp


def get_obs(deplot_env_obs, stats, depth=False, use_base=False):
    # >>>>>>>>>>>>>>>>> image resize <<<<<<<<<<<<<<<<<
    cur_bottom_rgb = deplot_env_obs['images']['cam_bottom']  # camera_extrinsics image
    cur_top_rgb = deplot_env_obs['images']['cam_top']  # camera_extrinsics image
    cur_left_wrist = deplot_env_obs['images']['cam_left_wrist']  # camera_extrinsics image
    cur_right_wrist = deplot_env_obs['images']['cam_right_wrist']  # camera_extrinsics image

    cur_bottom_rgb = cv2.resize(cv2.cvtColor(cur_bottom_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_top_rgb = cv2.resize(cv2.cvtColor(cur_top_rgb, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_left_wrist = cv2.resize(cv2.cvtColor(cur_left_wrist, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    cur_right_wrist = cv2.resize(cv2.cvtColor(cur_right_wrist, cv2.COLOR_BGRA2BGR), (320, 240))[:, :, ::-1]
    # cv2.imshow("image", np.hstack(
    #     [cur_right_wrist[..., ::-1], cur_left_wrist[..., ::-1], cur_top_rgb[..., ::-1]]))
    # cv2.waitKey(1)
    save_img(cur_left_wrist, cur_right_wrist, cur_top_rgb)

    # >>>>>>>>>>>>>>>>> state <<<<<<<<<<<<<<<<<
    cur_joint_positions = deplot_env_obs['qpos']
    if use_base:
        cur_joint_positions = np.hstack([deplot_env_obs['qpos'], deplot_env_obs['base']])
    cur_state = pre_process(cur_joint_positions, 'qpos', stats)
    cur_state = np.expand_dims(cur_state, axis=0)

    # >>>>>>>>>>>>>>>>> image crop and resize, similar to the train image preprocess <<<<<<<<<<<<<<<<<
    cur_left_wrist = np.array(cur_left_wrist)
    cur_right_wrist = np.array(cur_right_wrist)
    cur_top_rgb = np.array(cur_top_rgb)
    curr_images = np.array([cur_top_rgb, cur_left_wrist, cur_right_wrist])
    curr_images = torch.from_numpy(curr_images)
    curr_images = np.transpose(curr_images, (0, 3, 1, 2))
    original_size = curr_images.shape[-2:]
    ratio = 0.95
    curr_images = curr_images[...,
                  int(original_size[0] * (1 - ratio) / 2): int(original_size[0] * (1 + ratio) / 2),
                  int(original_size[1] * (1 - ratio) / 2): int(original_size[1] * (1 + ratio) / 2)]
    resize_transform = transforms.Resize(original_size, antialias=True)
    curr_images = resize_transform(curr_images)

    # >>>>>>>>>>>>>>>>> clip preprocess <<<<<<<<<<<<<<<<<
    traj_rgb = []
    for img in curr_images:
        # img = image_processor.preprocess(img, return_tensors='pt')['pixel_values'][0]
        img = image_processor((img / 255.0).float())
        traj_rgb.append(img)
    traj_rgb = torch.stack(traj_rgb, dim=0)
    traj_rgb = traj_rgb[None, ...]  # add bs=1

    return cur_state, traj_rgb


def save_img(cur_left_wrist, cur_right_wrist, cur_top_rgb):
    global idx
    Image.fromarray(cur_left_wrist).save(f"/home/<USER>/Documents/zhumj/eval_episode_img/left_wrist/{idx}.jpg")
    Image.fromarray(cur_right_wrist).save(f"/home/<USER>/Documents/zhumj/eval_episode_img/right_wrist/{idx}.jpg")
    Image.fromarray(cur_top_rgb).save(f"/home/<USER>/Documents/zhumj/eval_episode_img/top/{idx}.jpg")
    idx += 1


def eval_bc(config, ckpt_name, deploy_env):
    ckpt_dir = config['ckpt_dir']
    policy_class = config['policy_class']
    policy_config = config['policy_config']
    use_base = config['use_base']
    raw_lang = config['raw_lang']
    print("use base >>>>>>>>>>>>>>>>", use_base)

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< encode lang >>>>>>>>>>>>>>>>>>>>>>>>>>
    print("raw lang: ", raw_lang)
    encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
    outputs = lang_model(**encoded_input)
    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
    print(f'encoded_lang size: {encoded_lang.size()}')
    encoded_lang = encoded_lang.float()

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< load policy >>>>>>>>>>>>>>>>>>>>>>>>>>
    ckpt_path = os.path.join(ckpt_dir, ckpt_name)
    policy = make_policy(policy_class, policy_config)
    print(policy)
    checkpoint = torch.load(ckpt_path, map_location=torch.device('cpu'))
    if "compressed" in ckpt_path:
        loading_status = policy.deserialize(checkpoint, inference=True)
    else:
        loading_status = policy.deserialize(checkpoint["nets"], inference=True)
    policy.cuda()
    policy.eval()
    print(f'Loaded: {ckpt_path}')
    stats_path = os.path.join(ckpt_dir, f'dataset_stats.pkl')
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)

    # <<<<<<<<<<<<<<<<<<<<<<<<<<< action post process >>>>>>>>>>>>>>>>>>>>>>>>>>
    if policy_class == 'DroidDiffusion' or policy_class == "DroidDiT":
        if use_base:
            post_process = lambda a: ((a + 1) / 2) * (stats['action_max'][:16] - stats['action_min'][:16]) + stats['action_min'][:16]
        else:
            post_process = lambda a: ((a + 1) / 2) * (stats['action_max'][:14] - stats['action_min'][:14]) + stats['action_min'][:14]
    else:
        raise NotImplementedError

    query_frequency = policy_config['num_queries']

    if policy_class == "DroidDiffusion" or policy_class == "DroidDiT":
        query_frequency = query_frequency // 1

    action_queue = deque(maxlen=query_frequency)
    max_timesteps = 10000

    for rollout_id in range(100000):
        rollout_id += 0
        print(f"env has reset!")

        with torch.inference_mode():
            time0 = time.time()
            DT = 1 / FPS
            culmulated_delay = 0
            for t in range(max_timesteps):
                print(f"=============={t}")
                if t % 150 == 1:
                    a = input("q means next eval:")
                    if a == 'q':
                        # env.reset(randomize=False)
                        lang_in = input("Input the raw_lang(q means using default):")
                        if lang_in != 'q':
                            raw_lang = lang_in
                            encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
                            outputs = lang_model(**encoded_input)
                            encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
                            # [1, 768]
                            print(f'encoded_lang size: {encoded_lang.size()}')
                            encoded_lang = encoded_lang.float()

                time1 = time.time()

                # <<<<<<<<<<<<<<<<<<<<<<<<<<< get obs >>>>>>>>>>>>>>>>>>>>>>>>>>
                obs = deploy_env.get_obs()
                robot_state, traj_rgb = get_obs(obs, stats, use_base)
                robot_state = torch.from_numpy(robot_state).float().cuda()
                curr_image = traj_rgb.cuda()

                # <<<<<<<<<<<<<<<<<<<<<<<<<<< warm up >>>>>>>>>>>>>>>>>>>>>>>>>>
                if t == 0:
                    for _ in range(2):
                        policy(robot_state, curr_image, language_distilbert=encoded_lang)
                    print('network warm up done')

                # <<<<<<<<<<<<<<<<<<<<<<<<<<< query policy >>>>>>>>>>>>>>>>>>>>>>>>>>
                time1 = time.time()
                if config['policy_class'].find("Diffusion") >= 0 or config['policy_class'].find("DiT") >= 0:
                    if len(action_queue) == 0:
                        all_actions = policy(robot_state, curr_image, language_distilbert=encoded_lang).squeeze()[:query_frequency]
                        print(f"=======all_actions size: {all_actions.size()}")
                        action_queue.extend(all_actions)
                    raw_action = action_queue.popleft()
                else:
                    raise NotImplementedError

                print(f"raw action size: {raw_action.size()}")
                raw_action = raw_action.squeeze(0).cpu().to(dtype=torch.float32).numpy()
                action = post_process(raw_action)
                print(f"after post_process action size: {action.shape}")

                ### step the environment
                print(f'step {t}, pred action: {action}')
                action_info = deploy_env.step(action, mode="absolute")
                duration = time.time() - time1
                sleep_time = max(0, DT - duration)
                time.sleep(sleep_time)
                if duration >= DT:
                    culmulated_delay += (duration - DT)
                    print(
                        f'Warning: step duration: {duration:.3f} s at step {t} longer than DT: {DT} s, culmulated delay: {culmulated_delay:.3f} s')

            print(f'Avg fps {max_timesteps / (time.time() - time0)}')

    return


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt dir used for saving traing ckpts',
                        required=True)
    parser.add_argument('--policy_class', action='store', type=str,
                        help='the policy class used for imitation learning, DroidDiT, capitalize', required=True)
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)
    parser.add_argument('--batch_size', action='store', type=int, help='batch_size', required=True)
    parser.add_argument('--seed', action='store', type=int, help='seed', required=True)
    parser.add_argument('--num_steps', action='store', type=int, help='number of training steps', required=True)
    parser.add_argument('--lr', action='store', type=float, help='lr', required=True)
    parser.add_argument('--lr_backbone', default=1e-4, type=float, help='lr_backbone')
    parser.add_argument('--save_every', action='store', type=int, default=500, help='save_every', required=False)

    ### model hyperparameters
    parser.add_argument('--num_noise_samples', type=int, default=8)
    parser.add_argument('--chunk_size', action='store', type=int, help='chunk_size', required=False)
    parser.add_argument('--backbone', type=str, default='resnet18')
    parser.add_argument('--img_fea_dim', type=int, default=512)
    parser.add_argument('--cond_obs_dim', type=int, default=512)
    parser.add_argument('--model_size', default="DiT-S", type=str)
    parser.add_argument('--using_vit_film', action='store_true', default=False)

    ### data hyperparameters
    parser.add_argument('--image_size_w', type=int, default=640)
    parser.add_argument('--image_size_h', type=int, default=480)
    parser.add_argument('--bgr', type=int, default=0)
    parser.add_argument('--use_color_rand', action='store_true', default=False)

    ### state hyperparameters (input dim and output dim setup)
    parser.add_argument('--double_arm', type=int, default=0)
    parser.add_argument('--is_use_sub_reason', default=0, type=int)
    parser.add_argument('--use_base', default=0, type=int)
    parser.add_argument("--use_inspair_hand", type=int, help='if or not inspair hand ', default=0)
    parser.add_argument("--only_use_base", type=int, help='only train base action  ', default=0)

    ### pretrain and resume hyperparameters
    parser.add_argument('--load_pretrain', action='store_true', default=False)
    parser.add_argument('--resume', type=int, help='resume train', default=0)
    parser.add_argument('--load_pre_path', type=str, help='load pretrain model path', default=None)
    parser.add_argument("--use_constant", type=int, help='for optimizer shelduer', default=0)

    ### for eval
    parser.add_argument('--ckpt_name', type=str, default='null')
    parser.add_argument('--raw_lang', type=str, default='null')
    main(vars(parser.parse_args()))