import sys
import os
import h5py
import time
import numpy as np
import torch
import utils.torch_utils as TorchUtils


def convert_actions(pred_action):
    cur_xyz = pred_action[:3]
    cur_rot6d = pred_action[3:9]
    cur_gripper = np.expand_dims(pred_action[-1], axis=0)

    cur_rot6d = torch.from_numpy(cur_rot6d).unsqueeze(0)
    cur_euler = TorchUtils.rot_6d_to_euler_angles(rot_6d=cur_rot6d, convention="XYZ").squeeze().numpy()
    pred_action = np.concatenate((cur_xyz, cur_euler, cur_gripper))
    print(f'4. after convert pred_action: {pred_action}')

    return pred_action


def init_robot():
    sys.path.insert(0, "/home/<USER>/Dev-Code/droid")
    from droid.robot_env import RobotEnv

    policy_timestep_filtering_kwargs = {'action_space': 'cartesian_position', 'gripper_action_space': 'position',
                                        'robot_state_keys': ['cartesian_position', 'gripper_position',
                                                             'joint_positions']}
    # resolution (w, h)
    policy_camera_kwargs = {
        'hand_camera': {'image': True, 'concatenate_images': False, 'resolution': (480, 270), 'resize_func': 'cv2'},
        'varied_camera': {'image': True, 'concatenate_images': False, 'resolution': (480, 270), 'resize_func': 'cv2'}}

    deploy_env = RobotEnv(
        action_space=policy_timestep_filtering_kwargs["action_space"],
        gripper_action_space=policy_timestep_filtering_kwargs["gripper_action_space"],
        camera_kwargs=policy_camera_kwargs
    )

    deploy_env._robot.establish_connection()
    deploy_env.camera_reader.set_trajectory_mode()
    # exit()
    return deploy_env


if __name__ == '__main__':
    path = "/mnt/HDD/droid/h5_format_data/bin_pikcing_posiiton_480_640/bin_pikcing_posiiton_480_640_succ_t0001_s-0-0"
    # path = '/media/eai/Elements/robotics/bin_pikcing_posiiton_480_640/bin_pikcing_posiiton_480_640_succ_t0001_s-0-0'
    # path = '/media/eai/ADDS-4/zhouzy/data/expert_method_data/put_painter_position_480_640/put_painter_position_480_640_succ_t0001_s-0-0'
    # path = "/mnt/HDD/droid/h5_format_data/0115_sequenctial_blue_van_banana_marker_480_640/0115_sequenctial_blue_van_banana_marker_480_640_succ_t0001_s-0-0"
    # path = "/mnt/HDD/droid/h5_format_data/to_compress/0114_open_drawer_put_spider_man_480_640/0114_open_drawer_put_spider_man_480_640_succ_t0001_s-0-0"
    episode = "episode_0.hdf5"
    with h5py.File(os.path.join(path, episode), 'r') as f:
        actions = f['action'][:]
    env = init_robot()
    i = 0
    for act in actions:
        print(i)
        i += 1
        act = convert_actions(act)
        env.step(act)
        time.sleep(0.1)
        pass
