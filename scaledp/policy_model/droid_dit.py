import os
import math
import random

from typing import <PERSON><PERSON>

try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

import timm
import torch
import torch.nn as nn
import torch.utils.checkpoint
from torch.nn import functional as F

from vision_net.eva_warpper import FiLMedEvaWarpper
from vision_net.clip import CLIPVisionTower
from diffusers.schedulers.scheduling_ddim import DDIMScheduler
from diffusers.training_utils import EMAModel
from transformers.activations import ACT2FN

class DroidDiTPolicy(nn.Module):
    def __init__(self, args_override, rank=None):
        super().__init__()
        self.camera_names = args_override['camera_names']
        self.observation_horizon = args_override['observation_horizon']
        self.num_inference_timesteps = args_override['num_inference_timesteps']
        self.num_queries = args_override['num_queries']
        self.num_noise_samples = args_override['num_noise_samples']

        self.ema_power = args_override['ema_power']
        self.lr = args_override['lr']
        self.weight_decay = args_override['weight_decay']

        self.img_fea_dim = args_override['img_fea_dim']
        self.ac_dim = args_override['action_dim']
        self.state_dim = args_override['state_dim']
        self.cond_obs_dim = args_override['cond_obs_dim']

        self.backbone_name = args_override['backbone']
        self.lr_backbone = args_override['lr_backbone']
        self.cat_lang = False   # TODO
        self.using_vit_film = args_override.get("using_vit_film", False)
        print("using_vit_film: ", self.using_vit_film)
        # >>>>>>>>>>>>>>>>>>> Construct ViT Backbone <<<<<<<<<<<<<<<<<<<<
        if "clip" in self.backbone_name:
            backbones = CLIPVisionTower(self.backbone_name, self.camera_names)
            backbone_output_dim = backbones.hidden_size
        elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone_name:
            backbones = timm.create_model(
                self.backbone_name,
                pretrained=False,
                num_classes=0,  # remove classifier nn.Linear
            )
            pretrain_ckpt = torch.load(os.path.join(self.backbone_name, "pytorch_model.bin"))
            backbones.load_state_dict(pretrain_ckpt, strict=False)
            backbone_output_dim = backbones.num_features
        else:
            raise NotImplementedError

        # >>>>>>>>>>>>>>>>>>> Filmed ViT <<<<<<<<<<<<<<<<<<<<
        if self.using_vit_film:
            assert "vit_base_patch16_rope_reg1_gap_256" in self.backbone_name, "We only support vit rope in this version for wrapper vit to a filmedVit"
            assert not self.cat_lang
            new_backbones = FiLMedEvaWarpper(backbones, llm_dim=768)
            backbones = new_backbones
        linears = nn.Linear(backbone_output_dim, self.img_fea_dim)
        if self.cat_lang:
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim + 768
        else:
            self.obs_input_dim = self.img_fea_dim * len(self.camera_names) + self.state_dim
        combine = nn.Sequential(
            nn.Linear(self.obs_input_dim, 1024),
            nn.ReLU(),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, self.cond_obs_dim)
        )
        print("Build noise_pred_net ", args_override['model_size'])

        model_size = args_override['model_size']
        noise_pred_net = DiT_models[model_size](
            input_dim = self.ac_dim,
            output_dim = self.ac_dim,
            cond_dim = self.cond_obs_dim,
            horizon = self.num_queries,
        )
        print("Nosie predict net parameter number: ", sum(p.numel() for p in noise_pred_net.parameters()) / 1e6, "M")
        self.nets = nn.ModuleDict({
            'policy': nn.ModuleDict({
                'backbones': backbones,
                'linears': linears,
                'combine': combine,
                'noise_pred_net': noise_pred_net
            })
        })

        ENABLE_EMA = True
        if ENABLE_EMA:
            ema = EMAModel(model=self.nets.cuda(), power=self.ema_power)  # .to(device=rank)
        else:
            ema = None
        self.ema = ema

        self.noise_scheduler = DDIMScheduler(
            num_train_timesteps=100,
            beta_schedule='squaredcos_cap_v2',
            clip_sample=True,
            set_alpha_to_one=True,
            steps_offset=0,
            prediction_type='epsilon'
        )

    def configure_optimizers(self):
        param_dicts = [
            {
                "params": [p for n, p in self.nets.named_parameters() if "backbone" not in n and p.requires_grad]
            },
            {
                "params": [p for n, p in self.nets.named_parameters() if "backbone" in n and p.requires_grad],
                "lr": self.lr_backbone,
            },
        ]
        optimizer = torch.optim.AdamW(param_dicts, lr=self.lr,
                                      weight_decay=self.weight_decay)
        return optimizer

    def timm_forward(self, images, lang_embed):
        "images: B, views, c, h, w"
        b = images.shape[0]
        views = images.shape[1]
        assert images.ndim == 5  # b views c h w

        feature_list = []
        for j in range(views):
            image = images[:, j]
            if not self.using_vit_film:
                image_forward_out = self.nets['policy']["backbones"].forward_features(image)
            else:
                image_forward_out = self.nets['policy']["backbones"](image, lang_embed)
            image_feature = image_forward_out[:, :1].to(images.dtype)
            image_feature = image_feature.mean(dim=1)  # mean along the token dimension
            feature_list.append(image_feature)
        image_features = torch.stack(feature_list, dim=1)

        return image_features

    def __call__(self, qpos, image, actions=None, is_pad=None, language_distilbert=None):
        lang_embed = language_distilbert
        B = qpos.shape[0]
        if actions is not None:  # training time
            actions = actions[:, :self.num_queries, :self.ac_dim]
            is_pad = is_pad[:, :self.num_queries]

            # encode image
            if "clip" in self.backbone_name:
                all_cam_features = self.nets['policy']["backbones"](image)
            elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone_name:
                all_cam_features = self.timm_forward(image, lang_embed)
            else:
                raise NotImplementedError
            all_cam_features = self.nets['policy']['linears'](all_cam_features)
            all_cam_features = all_cam_features.flatten(start_dim=1)

            # concat with other conditions
            if self.cat_lang:
                obs_cond = torch.cat([all_cam_features] + [qpos] + [lang_embed], dim=1)
            else:
                obs_cond = torch.cat([all_cam_features] + [qpos], dim=1)
            obs_cond = self.nets['policy']['combine'](obs_cond)

            # sample noise to add to actions
            noise = torch.randn([self.num_noise_samples] + list(actions.shape), device=obs_cond.device)

            # sample a diffusion iteration for each data point
            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps,
                (B,), device=obs_cond.device
            ).long()

            # add noise to the clean actions according to the noise magnitude at each diffusion iteration
            timesteps, noise = timesteps.to(obs_cond.device), noise.to(obs_cond.device)

            noisy_actions = torch.cat([self.noise_scheduler.add_noise(
                actions, noise[i], timesteps)
                for i in range(len(noise))], dim=0)
            position_ids = torch.arange(self.num_queries, device=obs_cond.device)[None, :].repeat(B * self.num_noise_samples, 1)

            obs_cond = obs_cond.repeat(self.num_noise_samples, 1)
            timesteps = timesteps.repeat(self.num_noise_samples)
            is_pad = is_pad.repeat(self.num_noise_samples, 1)

            # predict the noise residual
            noise_pred = self.nets['policy']['noise_pred_net'](noisy_actions, timesteps, global_cond=obs_cond, position_ids=position_ids)

            # L2 loss
            noise = noise.view(noise.size(0) * noise.size(1), *noise.size()[2:])
            all_l2 = F.mse_loss(noise_pred, noise, reduction='none')
            loss = (all_l2 * ~is_pad.unsqueeze(-1)).mean()

            loss_dict = {}
            loss_dict['l2_loss'] = loss
            loss_dict['loss'] = loss

            if self.training and self.ema is not None:
                self.ema.step(self.nets)
            return loss_dict
        else:  # inference time
            To = self.observation_horizon
            Tp = self.num_queries
            action_dim = self.ac_dim

            nets = self.nets
            if self.ema is not None:
                nets = self.ema.averaged_model

            if "clip" in self.backbone_name:
                all_cam_features = nets['policy']["backbones"](image)
            elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone_name:
                images = image
                b = images.shape[0]
                views = images.shape[1]
                assert images.ndim == 5  # b views c h w

                feature_list = []
                for j in range(views):
                    image = images[:, j]
                    if not self.using_vit_film:
                        image_forward_out = nets['policy']["backbones"].forward_features(image)
                    else:
                        image_forward_out = nets['policy']["backbones"](image, lang_embed)
                    image_feature = image_forward_out[:, :1].to(images.dtype)
                    image_feature = image_feature.mean(dim=1)  # mean along the token dimension
                    feature_list.append(image_feature)
                all_cam_features = torch.stack(feature_list, dim=1)
            else:
                raise NotImplementedError
            all_cam_features = nets['policy']['linears'](all_cam_features)
            all_cam_features = all_cam_features.flatten(start_dim=1) #

            if self.cat_lang:
                obs_cond = torch.cat([all_cam_features] + [qpos] + [lang_embed], dim=1)
            else:
                obs_cond = torch.cat([all_cam_features] + [qpos], dim=1)
            obs_cond = nets['policy']['combine'](obs_cond)

            # initialize action from Guassian noise
            noisy_action = torch.randn(
                (B, Tp, action_dim), device=obs_cond.device)
            naction = noisy_action
            position_ids = torch.arange(self.num_queries, device=obs_cond.device)[None, :].repeat(B, 1)

            # init scheduler
            self.noise_scheduler.set_timesteps(self.num_inference_timesteps)

            for k in self.noise_scheduler.timesteps:
                # predict noise
                noise_pred = nets['policy']['noise_pred_net'](
                    x=naction,
                    t=k,
                    global_cond=obs_cond,
                    position_ids=position_ids
                )

                # inverse diffusion step (remove noise)
                naction = self.noise_scheduler.step(
                    model_output=noise_pred,
                    timestep=k,
                    sample=naction
                ).prev_sample

            return naction

    def serialize(self):
        return {
            "nets": self.nets.state_dict(),
            "ema": self.ema.averaged_model.state_dict() if self.ema is not None else None,
        }

    def deserialize(self, model_dict, inference=False,steps=0):
        if model_dict.get("nets", None) is not None:
            status = self.nets.load_state_dict(model_dict["nets"])
            print('Loaded nets')
        if inference:
            status = None
            assert model_dict.get("ema", None) is not None, "Inference requires EMA model"
            print("==============Inference Time================")
        if model_dict.get("ema", None) is not None:
            print('Loaded EMA')
            status_ema = self.ema.averaged_model.load_state_dict(model_dict["ema"], strict=True)
            self.ema.optimization_step=steps
            status = [status, status_ema]
        return status


#################################################################################
#                           Scalable Diffusion Policy                           #
#################################################################################

# copied from transformres.models.gemma.modeling_gemma.py
class GemmaRMSNorm(nn.Module):
    def __init__(self, dim: int, eps: float = 1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.zeros(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        output = self._norm(x.float())
        # Llama does x.to(float16) * w whilst Gemma2 is (x * w).to(float16)
        # See https://github.com/huggingface/transformers/pull/29402
        output = output * (1.0 + self.weight.float())
        return output.type_as(x)

    def extra_repr(self):
        return f"{tuple(self.weight.shape)}, eps={self.eps}"


class GemmaMLP(nn.Module):
    def __init__(self, in_features, hidden_features, hidden_act):
        super().__init__()
        self.hidden_size = in_features
        self.intermediate_size = hidden_features
        self.gate_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.up_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.down_proj = nn.Linear(self.intermediate_size, self.hidden_size, bias=False)
        self.act_fn = ACT2FN[hidden_act]

    def forward(self, x):
        down_proj = self.down_proj(self.act_fn(self.gate_proj(x)) * self.up_proj(x))
        return down_proj

def modulate(x, shift, scale):
    return x * (1 + scale.unsqueeze(1)) + shift.unsqueeze(1)


#################################################################################
#               Embedding Layers for Timesteps and Class Labels                 #
#################################################################################

class TimestepEmbedder(nn.Module):
    """
    Embeds scalar timesteps into vector representations.
    """

    def __init__(self, hidden_size, frequency_embedding_size=256):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(frequency_embedding_size, hidden_size, bias=True),
            nn.SiLU(),
            nn.Linear(hidden_size, hidden_size, bias=True),
        )
        self.frequency_embedding_size = frequency_embedding_size

    @staticmethod
    def timestep_embedding(t, dim, max_period=10000):
        """
        Create sinusoidal timestep embeddings.
        :param t: a 1-D Tensor of N indices, one per batch element.
                          These may be fractional.
        :param dim: the dimension of the output.
        :param max_period: controls the minimum frequency of the embeddings.
        :return: an (N, D) Tensor of positional embeddings.
        """
        # https://github.com/openai/glide-text2im/blob/main/glide_text2im/nn.py
        half = dim // 2
        freqs = torch.exp(
            -math.log(max_period) * torch.arange(start=0, end=half, dtype=torch.float32) / half
        ).to(device=t.device)
        args = t[:, None].float() * freqs[None]
        embedding = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        if dim % 2:
            embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
        return embedding

    def forward(self, t):
        t_freq = self.timestep_embedding(t, self.frequency_embedding_size)
        t_emb = self.mlp(t_freq)
        return t_emb


#################################################################################
#                                 Core DiT Model                                #
#################################################################################
def repeat_kv(hidden_states: torch.Tensor, n_rep: int) -> torch.Tensor:
    """
    This is the equivalent of torch.repeat_interleave(x, dim=1, repeats=n_rep). The hidden states go from (batch,
    num_key_value_heads, seqlen, head_dim) to (batch, num_attention_heads, seqlen, head_dim)
    """
    batch, num_key_value_heads, slen, head_dim = hidden_states.shape
    if n_rep == 1:
        return hidden_states
    hidden_states = hidden_states[:, :, None, :, :].expand(batch, num_key_value_heads, n_rep, slen, head_dim)
    return hidden_states.reshape(batch, num_key_value_heads * n_rep, slen, head_dim)

def _compute_default_rope_parameters(
    device = None,
    base = None,
    dim = None,
):
    """
    Returns:
        Tuple of (`torch.Tensor`, `float`), containing the inverse frequencies for the RoPE embeddings and the
        post-processing scaling factor applied to the computed cos/sin (unused in this type of RoPE).
    """
    attention_factor = 1.0  # Unused in this type of RoPE
    # Compute the inverse frequencies
    inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2, dtype=torch.int64).to(device=device, dtype=torch.float) / dim))
    return inv_freq, attention_factor

def rotate_half(x):
    """Rotates half the hidden dims of the input."""
    x1 = x[..., : x.shape[-1] // 2]
    x2 = x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)


def apply_rotary_pos_emb(q, k, cos, sin, position_ids=None, unsqueeze_dim=1):
    """Applies Rotary Position Embedding to the query and key tensors.

    Args:
        q (`torch.Tensor`): The query tensor.
        k (`torch.Tensor`): The key tensor.
        cos (`torch.Tensor`): The cosine part of the rotary embedding.
        sin (`torch.Tensor`): The sine part of the rotary embedding.
        position_ids (`torch.Tensor`, *optional*):
            Deprecated and unused.
        unsqueeze_dim (`int`, *optional*, defaults to 1):
            The 'unsqueeze_dim' argument specifies the dimension along which to unsqueeze cos[position_ids] and
            sin[position_ids] so that they can be properly broadcasted to the dimensions of q and k. For example, note
            that cos[position_ids] and sin[position_ids] have the shape [batch_size, seq_len, head_dim]. Then, if q and
            k have the shape [batch_size, heads, seq_len, head_dim], then setting unsqueeze_dim=1 makes
            cos[position_ids] and sin[position_ids] broadcastable to the shapes of q and k. Similarly, if q and k have
            the shape [batch_size, seq_len, heads, head_dim], then set unsqueeze_dim=2.
    Returns:
        `tuple(torch.Tensor)` comprising of the query and key tensors rotated using the Rotary Position Embedding.
    """
    cos = cos.unsqueeze(unsqueeze_dim)
    sin = sin.unsqueeze(unsqueeze_dim)
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed


class RotaryEmbedding(nn.Module):
    def __init__(self, hidden_dim, base=10000.0, device=None):
        super().__init__()
        inv_freq, self.attention_scaling = _compute_default_rope_parameters(base=base, dim=hidden_dim, device=device)
        self.register_buffer("inv_freq", inv_freq, persistent=False)

    @torch.no_grad()
    def forward(self, x, position_ids):
        inv_freq_expanded = self.inv_freq[None, :, None].float().expand(position_ids.shape[0], -1, 1).to(x.device)
        position_ids_expanded = position_ids[:, None, :].float()

        device_type = x.device.type if isinstance(x.device.type, str) and x.device.type != "mps" else "cpu"
        with torch.autocast(device_type=device_type, enabled=False):  # Force float32
            freqs = (inv_freq_expanded.float() @ position_ids_expanded.float()).transpose(1, 2)
            emb = torch.cat((freqs, freqs), dim=-1)
            cos = emb.cos() * self.attention_scaling
            sin = emb.sin() * self.attention_scaling

        return cos.to(dtype=x.dtype), sin.to(dtype=x.dtype)


class GroupedAttention(nn.Module):
    def __init__(
            self,
            dim: int,
            num_heads: int = 8,
            head_dim: int = 256,
            num_key_value_heads: int = 8,
            qkv_bias: bool = False,
            attn_drop: float = 0.,
    ) -> None:
        super().__init__()
        self.hidden_size = dim
        self.num_heads = num_heads
        self.head_dim = head_dim
        self.num_key_value_heads = num_key_value_heads
        self.num_key_value_groups = self.num_heads // self.num_key_value_heads

        self.scale = self.head_dim ** -0.5
        self.q_proj = nn.Linear(self.hidden_size, self.num_heads * self.head_dim, bias=qkv_bias)
        self.k_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=qkv_bias)
        self.v_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=qkv_bias)
        self.o_proj = nn.Linear(self.num_heads * self.head_dim, self.hidden_size, bias=qkv_bias)
        self.attn_drop = attn_drop

    def flash_attention_forward(
            self, attention_mask, batch_size, head_dim, query_states, key_states, value_states
    ):
        raise NotImplementedError("FA2 is not implemented (yet)")


    def eager_attention_forward(
            self, attention_mask, batch_size, head_dim, query_states, key_states, value_states
    ):
        num_att_heads = self.num_attention_heads # num heads of q
        num_key_value_heads = self.num_key_value_heads # num heads of k,v
        num_key_value_groups = num_att_heads // num_key_value_heads

        # query_states: batch_size, sequence_length, num_att_head, head_dim
        # key_states: batch_size, sequence_length, num_key_value_head, head_dim
        # value_states: batch_size, sequence_length, num_key_value_head, head_dim
        sequence_length = key_states.shape[1]

        key_states = key_states[:, :, :, None, :].expand(
            batch_size, sequence_length, num_key_value_heads, num_key_value_groups, head_dim
        )
        key_states = key_states.reshape(
            batch_size, sequence_length, num_key_value_heads * num_key_value_groups, head_dim
        )

        value_states = value_states[:, :, :, None, :].expand(
            batch_size, sequence_length, num_key_value_heads, num_key_value_groups, head_dim
        )
        value_states = value_states.reshape(
            batch_size, sequence_length, num_key_value_heads * num_key_value_groups, head_dim
        )

        # Attention here is upcasted to float32 to match the original eager implementation.

        query_states = query_states.to(dtype=torch.float32)
        key_states = key_states.to(dtype=torch.float32)

        query_states = query_states.transpose(1, 2)
        key_states = key_states.transpose(1, 2)

        att_weights = torch.matmul(query_states, key_states.transpose(2, 3))
        att_weights *= head_dim ** -0.5
        big_neg = -2.3819763e38  # See gemma/modules.py

        masked_att_weights = torch.where(attention_mask[:, None, :, :], att_weights, big_neg)

        probs = nn.functional.softmax(masked_att_weights, dim=-1)
        probs = probs.to(dtype=value_states.dtype)

        # probs: batch_size, num_key_value_head, num_att_head, sequence_length, sequence_length
        # value_states: batch_size, sequence_length, num_att_heads, head_dim

        att_output = torch.matmul(probs, value_states.permute(0, 2, 1, 3))

        att_output = att_output.permute(0, 2, 1, 3)
        # we use -1 because sequence length can change
        att_output = att_output.reshape(batch_size, -1, num_key_value_heads * num_key_value_groups * head_dim)

        return att_output


    def forward(self, noisy_actions: torch.Tensor, pos: torch.Tensor, attn_mask=None) -> torch.Tensor:
        bsz, q_len, _ = noisy_actions.size()
        query_states = self.q_proj(noisy_actions)
        key_states = self.k_proj(noisy_actions)
        value_states = self.v_proj(noisy_actions)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        cos, sin = pos
        query_states, key_states = apply_rotary_pos_emb(query_states, key_states, cos, sin)
        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)

        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) * self.scale
        if attn_mask is not None:
            attn_weights += attn_mask

        # upcast attention to fp32
        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)
        attn_weights = nn.functional.dropout(attn_weights, p=self.attn_drop if self.training else 0.0, training=self.training)
        attn_output = torch.matmul(attn_weights, value_states)

        if attn_output.size() != (bsz, self.num_heads, q_len, self.head_dim):
            raise ValueError(
                f"`attn_output` should be of size {(bsz, self.num_heads, q_len, self.head_dim)}, but is"
                f" {attn_output.size()}"
            )

        attn_output = attn_output.transpose(1, 2).contiguous()

        attn_output = attn_output.view(bsz, q_len, -1)
        attn_output = self.o_proj(attn_output)
        return attn_output

class Attention(nn.Module):
    def __init__(
            self,
            dim: int,
            num_heads: int = 8,
            head_dim: int = 256,
            qkv_bias: bool = False,
            attn_drop: float = 0.,
    ) -> None:
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = head_dim
        self.scale = self.head_dim ** -0.5

        self.qkv = nn.Linear(dim, head_dim * num_heads * 3, bias=qkv_bias)
        self.attn_drop = attn_drop
        self.proj = nn.Linear(head_dim * num_heads, dim)

    def forward(self, x: torch.Tensor, pos: torch.Tensor, attn_mask=None) -> torch.Tensor:
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv.unbind(0)

        cos, sin = pos
        q, k = apply_rotary_pos_emb(q, k, cos, sin)
        attn_weights = torch.matmul(q, k.transpose(2, 3)) * self.scale

        if attn_mask is not None:
            attn_weights += attn_mask

        # upcast attention to fp32
        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(q.dtype)
        attn_weights = nn.functional.dropout(attn_weights, p=self.attn_drop if self.training else 0.0, training=self.training)
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(B, N, -1).contiguous()
        attn_output = self.proj(attn_output)
        return attn_output


class DiTBlockRope(nn.Module):
    """
    A DiT block with adaptive layer norm zero (adaLN-Zero) conditioning.
    """

    def __init__(self, hidden_size, head_dim=256, num_heads=8, mlp_ratio=4.0, use_group_attention=False, num_key_value_heads=None, **block_kwargs):
        super().__init__()
        self.norm1 = GemmaRMSNorm(hidden_size, eps=1e-6)
        if use_group_attention:
            assert num_key_value_heads is not None
            self.attn = GroupedAttention(
                hidden_size, head_dim=head_dim, num_heads=num_heads, qkv_bias=False,
                num_key_value_heads=num_key_value_heads,
                **block_kwargs)
        else:
            self.attn = Attention(hidden_size, head_dim=head_dim, num_heads=num_heads, qkv_bias=False, **block_kwargs)
        self.norm2 = GemmaRMSNorm(hidden_size, eps=1e-6)
        mlp_hidden_dim = int(hidden_size * mlp_ratio)

        self.mlp = GemmaMLP(in_features=hidden_size, hidden_features=mlp_hidden_dim, hidden_act="gelu_pytorch_tanh")
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(),
            nn.Linear(hidden_size, 6 * hidden_size, bias=True)
        )

    def forward(self, x, c, pos, attn_mask=None):
        shift_msa, scale_msa, gate_msa, shift_mlp, scale_mlp, gate_mlp = self.adaLN_modulation(c).chunk(6, dim=1)
        x = x + gate_msa.unsqueeze(1) * self.attn(modulate(self.norm1(x), shift_msa, scale_msa), pos, attn_mask=attn_mask)
        x = x + gate_mlp.unsqueeze(1) * self.mlp(modulate(self.norm2(x), shift_mlp, scale_mlp))
        return x


class FinalLayer(nn.Module):
    """
    The final layer of DiT.
    """

    def __init__(self, hidden_size, output_dim):
        super().__init__()
        self.norm_final = GemmaRMSNorm(hidden_size, eps=1e-6)
        self.linear = nn.Linear(hidden_size, output_dim, bias=True)
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(),
            nn.Linear(hidden_size, 2 * hidden_size, bias=True)
        )

    def forward(self, x, c):
        shift, scale = self.adaLN_modulation(c).chunk(2, dim=1)
        x = modulate(self.norm_final(x), shift, scale)
        x = self.linear(x)
        return x


class DiT(nn.Module):
    """
    Diffusion model with a Transformer backbone.
    """
    def __init__(
            self,
            input_dim: int = 10,  # action dim
            output_dim: int = 10,  # action dim
            cond_dim: int = 0,  # the input dim of the condition
            horizon: int = 10,  # horizon
            depth: int = 28,  # number of DiT blocks
            n_emb: int = 256,  # embedding size
            head_dim: int = 256,
            num_heads: int = 16,
            mlp_ratio: int = 4.0,
            time_as_cond: bool = True,
            use_group_attention=False,
            num_key_value_heads=None
    ):
        super().__init__()
        # mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
        # mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
        # self.register_buffer("mask", mask)

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_heads = num_heads

        self.x_embedder = nn.Linear(input_dim, n_emb)
        self.t_embedder = TimestepEmbedder(n_emb)
        self.cond_obs_emb = nn.Linear(cond_dim, n_emb)

        self.rotary_emb = RotaryEmbedding(hidden_dim=head_dim)

        self.blocks = nn.ModuleList([
            DiTBlockRope(n_emb, head_dim, num_heads,
                         use_group_attention=use_group_attention,
                         num_key_value_heads=num_key_value_heads,
                         mlp_ratio=mlp_ratio
                         ) for _ in range(depth)
        ])
        self.final_layer = FinalLayer(n_emb, output_dim=output_dim)
        self.initialize_weights()
        # constants
        self.horizon = horizon
        self.time_as_cond = time_as_cond

    def  count_parameters(self):
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        return total_params, trainable_params

    def initialize_weights(self):
        # Initialize transformer layers:
        def _basic_init(module):
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

        self.apply(_basic_init)

        # Initialize patch_embed like nn.Linear (instead of nn.Conv2d):
        w = self.x_embedder.weight.data
        nn.init.xavier_uniform_(w.view([w.shape[0], -1]))
        nn.init.constant_(self.x_embedder.bias, 0)

        # Initialize label embedding table:
        nn.init.normal_(self.cond_obs_emb.weight, mean=0.0, std=0.02)
        nn.init.constant_(self.cond_obs_emb.bias, 0)

        # Initialize timestep embedding MLP:
        nn.init.normal_(self.t_embedder.mlp[0].weight, std=0.02)
        nn.init.normal_(self.t_embedder.mlp[2].weight, std=0.02)

        # Zero-out adaLN modulation layers in DiT blocks:
        for block in self.blocks:
            nn.init.constant_(block.adaLN_modulation[-1].weight, 0)
            nn.init.constant_(block.adaLN_modulation[-1].bias, 0)

        # Zero-out output layers:
        nn.init.constant_(self.final_layer.adaLN_modulation[-1].weight, 0)
        nn.init.constant_(self.final_layer.adaLN_modulation[-1].bias, 0)
        nn.init.constant_(self.final_layer.linear.weight, 0)
        # torch.nn.init.xavier_uniform_(self.final_layer.linear.weight)
        nn.init.constant_(self.final_layer.linear.bias, 0)

    def get_optim_groups(self, weight_decay: float = 1e-3):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, Attention)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = "%s.%s" % (mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.startswith("bias"):
                    # MultiheadAttention bias starts with "bias"
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)


        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert (
                len(inter_params) == 0
        ), "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert (
                len(param_dict.keys() - union_params) == 0
        ), "parameters %s were not separated into either decay/no_decay set!" % (
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(list(decay))],
                "weight_decay": weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(list(no_decay))],
                "weight_decay": 0.0,
            },
        ]
        return optim_groups

    def configure_optimizers(self,
                             learning_rate: float = 1e-4,
                             weight_decay: float = 1e-3,
                             betas: Tuple[float, float] = (0.9, 0.95)):
        optim_groups = self.get_optim_groups(weight_decay=weight_decay)
        optimizer = torch.optim.AdamW(
            optim_groups, lr=learning_rate, betas=betas
        )
        return optimizer

    def forward(self, x, t, global_cond, position_ids):
        """
        Forward pass of DiT.
        x: (N, T, input_dim)
        t: (N,) tensor of diffusion timesteps
        global_cond: (N, n_obs_steps, D) tensor of conditions: image embeddings
        """
        if not torch.is_tensor(t):
            # TODO: this requires sync between CPU and GPU. So try to pass timesteps as tensors if you can
            t = torch.tensor([t], dtype=torch.long, device=x.device)
        elif torch.is_tensor(t) and len(t.shape) == 0:
            t = t[None].to(x.device)

        t = t.expand(t.shape[0])
        x = self.x_embedder(x)  # (N, T, D), where T = horizon
        t = self.t_embedder(t)  # (N, D)
        global_cond = self.cond_obs_emb(global_cond)  # (N, D)
        position_embeddings = self.rotary_emb(x, position_ids)

        c = t + (global_cond.sum(dim=1) if len(global_cond.shape) == 3 else global_cond)  # (N, D)
        for block in self.blocks:
            # x = block(x, c, attn_mask=self.mask)  # (N, T, D)
            x = block(x, c, position_embeddings, attn_mask=None)  # (N, T, D)
        x = self.final_layer(x, c)  # (N, T, output_dim)
        return x


#################################################################################
#                                   DiT Configs                                  #
#################################################################################

def DiT_GemmaGA(**kwargs):
    return DiT(depth=18, n_emb=1024, head_dim=256, num_heads=8,
               use_group_attention=True, num_key_value_heads=1,
               **kwargs)

def DiT_Gemma(**kwargs):
    return DiT(depth=18, n_emb=1024, head_dim=256, num_heads=8, **kwargs)

def DiT_X(**kwargs):
    return DiT(depth=16, n_emb=1536, num_heads=12, **kwargs)

def DiT_H(**kwargs):
    return DiT(depth=32, n_emb=1280, num_heads=16, **kwargs)


def DiT_L(**kwargs):
    return DiT(depth=24, n_emb=1024, num_heads=16, **kwargs)


def DiT_B(**kwargs):
    return DiT(depth=12, n_emb=768, num_heads=12, **kwargs)


def DiT_S(**kwargs):
    return DiT(depth=12, n_emb=384, num_heads=6, **kwargs)


def DiT_Ti(**kwargs):
    return DiT(depth=8, n_emb=256, num_heads=4, **kwargs)


DiT_models = {
    'RDiT-H': DiT_H, 'RDiT-L': DiT_L, 'RDiT-B': DiT_B, 'RDiT-S': DiT_S, "RDiT-Ti": DiT_Ti, 'RDiT-X': DiT_X,
    'RDiT-Gemma': DiT_Gemma, "RDiT-GemmaGA": DiT_GemmaGA
}
