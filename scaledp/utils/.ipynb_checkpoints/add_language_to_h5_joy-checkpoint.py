import h5py
import os
from tqdm import tqdm
import random

import torch
from transformers import AutoTokenizer, AutoModel


tokenizer = AutoTokenizer.from_pretrained('/data/team/ljm/distilbert-base-uncased')
model = AutoModel.from_pretrained("/data/team/ljm/distilbert-base-uncased", torch_dtype=torch.float16)
model.to('cuda')

random_subtask_reasoning = [
    "<test_sub> Press down on the left corner of the garment and lift the clothing",
    "<test_sub> Lift both ends and shake the garment",
    "<test_sub> Align the left edge to the center",
    "<test_sub> Align the right edge to the center",
    "<test_sub> Fold the garment in half",
    "<test_sub> Fold it in half again",
    "<test_sub> Grab the clothing with your right hand and move it to the right side"]

def remove_key(file_path, delete_keys):
    print(file_path)
    with h5py.File(file_path, 'a') as f:
        print(f.attrs.keys())
        # 检查 key 是否存在
        for delete_key in delete_keys:
            if delete_key in f.keys():
                # 删除 key
                del f[delete_key]
                print(f"已删除 key: {delete_key}")
            else:
                print("key 不存在")



def add_attributes_to_hdf5(file_path, raw_lang, encoded_lang):
    print("file_path,", file_path)
    # Open the HDF5 file in read/write mode
    with h5py.File(file_path, 'a') as hdf5_file:
        # Add attributes to the root of the HDF5 file
        # for key, value in attributes.items():
        #    hdf5_file.create_dataset("language_raw", data=[raw_lang])
        encoded_lang = encoded_lang.cpu().detach().numpy()
        key = 'language_distilbert'
        # print("language_distilbert",encoded_lang)

        print(f"h5 keys: {hdf5_file.keys()}")
        if key not in hdf5_file.keys():
            hdf5_file.create_dataset("language_distilbert", data=[encoded_lang])
        key = 'language_raw'
        print(f"h5 keys: {hdf5_file.keys()}")
        if key not in hdf5_file.keys():
            hdf5_file.create_dataset("language_raw", data=[raw_lang])



def add_sub_reason_distilbert_h5py(file_path, raw_lang, encode_lang, tokenizer):
    print("file_path,", file_path)
    with h5py.File(file_path, 'a') as hdf5_file:

        sub_reasoning_list = []
        random_sub_reasoning_list = []
        key = 'sub_reason_distilbert'
        # if key in hdf5_file.keys():
        #     del hdf5_file[key]
        if key not in hdf5_file.keys():
            for tmp_key in hdf5_file['state/joint_position'].keys():
                a = tmp_key
                break
            for item in range(len(hdf5_file[f'state/joint_position/{a}'])):
                # if 'substep_reasonings' not in hdf5_file.keys():
                #     encoded_input = tokenizer(hdf5_file['reasoning'][0].decode('utf-8'),
                #                               return_tensors='pt').to('cuda')
                # else:
                #     reason = hdf5_file['substep_reasonings'][item].decode('utf-8')
                #     print("reason",reason)
                #     encoded_input = tokenizer(reason,
                #                           return_tensors='pt').to('cuda')
                if 'substep_reasonings' not in hdf5_file.keys():
                    random_selected_substep = random.choice(random_subtask_reasoning)  # TODO
                    random_sub_reasoning_list.append(random_selected_substep)  
                    encoded_input = tokenizer(random_selected_substep,
                                              return_tensors='pt').to('cuda')
                    outputs = model(**encoded_input)
                    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
                    encoded_lang = encoded_lang.cpu().detach().numpy()
                    sub_reasoning_list.append(encoded_lang)
                else:
                    continue
            if 'substep_reasonings' not in hdf5_file.keys():
                hdf5_file.create_dataset('substep_reasonings', data=random_sub_reasoning_list)  # TODO  
                hdf5_file.create_dataset(key, data=sub_reasoning_list)
        else:
            print("key exists")

if __name__ == "__main__":
    file_path1 = "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels"
    tasks1 = {
        "20250530_random_fold_stacked_T-shirts_zby_compressed": "Fold the T-shirt.",
        "20250603_random_fold_stacked_T-shirts_zby_compressed": "Fold the T-shirt.",
        "20250603_random_fold_stacked_T-shirts_zby_2_compressed": "Fold the T-shirt.",
    }

    file_path2 = "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels"
    tasks2 = {
        "20250521_fold_pants_zby_compressed": "Fold the pants.",
        "20250522_fold_pants_zby_compressed": "Fold the pants.",
        "20250523_fold_pants_zby_compressed": "Fold the pants.",
        "20250526_fold_pants_lyp_compressed": "Fold the pants.",
        "20250526_fold_pants_zby_compressed": "Fold the pants.",
        "20250527_fold_pants_lyp_compressed": "Fold the pants.",
        "20250527_fold_pants_zby_compressed": "Fold the pants.",
        "20250528_fold_T-shirts_zby_compressed": "Fold the T-shirt.",
        "20250529_fold_T-shirts_lyp_compressed": "Fold the T-shirt.",
        "20250529_fold_T-shirts_zby_compressed": "Fold the T-shirt."
    }
    file_path3="/data/private/joy/data_test"
    tasks3={"task": "Fold the pants."}
    file_path_list=[file_path3]
    task_list=[tasks3]
    # file_path_list=[file_path1,file_path2]
    # task_list=[tasks1,tasks2]

    # file_path = "/data/efs/qiaoyi/EAI_robot_data/static_aloha"
    # tasks = {
    #     "20250526_random_folding_pants_Leo_compressed": "Fold the pants.",
    #     "20250527_random_folding_pants_Leo_compressed": "Fold the pants.",
    #     "20250528_random_folding_pants_Leo_compressed": "Fold the pants.",
    #     "20250528_random_folding_pants_zjm_2_compressed": "Fold the pants.",
    #     "20250528_random_folding_pants_zjm_compressed": "Fold the pants.",
    #     "20250529_random_folding_pants_Leo_compressed": "Fold the pants.",
    #     "20250529_random_folding_pants_zjm_2_compressed": "Fold the pants.",
    #     "20250529_random_folding_pants_zjm_compressed": "Fold the pants.",
    #     "20250530_random_folding_pants_zjm_compressed": "Fold the pants.",
    #     "20250603_random_folding_pants_lyp_compressed": "Fold the pants.",
    #     "20250603_random_folding_pants_zjm_compressed": "Fold the pants.",
    #     "folding_shirts_stack_Leo_20250522_compressed": "Fold the T-shirt.",
    #     "folding_shirts_stack_zjm_20250522_compressed": "Fold the T-shirt.",
    #     "folding_shirts_stack_zjm_20250523_compressed": "Fold the T-shirt.",
    #     "random_folding_pants_Leo_20250526_noon_compressed": "Fold the pants.",
    #     "random_folding_pants_zjm_20250526_2_compressed": "Fold the pants.",
    #     "random_folding_pants_zjm_20250526_compressed": "Fold the pants.",
    #     "random_folding_pants_zjm_20250527_2_compressed": "Fold the pants.",
    #     "random_folding_pants_zjm_20250527_compressed": "Fold the pants."
    # }
    for file_path,tasks in zip(file_path_list, task_list):
        encoding_list=[]

        use_key_raw = False


        for t, lang in tasks.items():
            print("=" * 40)
            raw_lang = lang
            print(f'raw_lang: {raw_lang}')
            encoded_input = tokenizer(raw_lang, return_tensors='pt').to('cuda')
            outputs = model(**encoded_input)
            encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
            # [1, 768]
            print(f'encoded_lang size: {encoded_lang.size()}')
            # cfg['lang_intrs_distilbert'] = encoded_lang

            t_p = os.path.join(file_path, t)
            episodes = os.listdir(t_p)
            for ep in tqdm(episodes):
                if not ep.endswith('.hdf5'):
                    continue
                remove_key(os.path.join(file_path, t, ep), ['substep_reasonings', 'sub_reason_distilbert'])
                print('remove')
                ep_p = os.path.join(t_p, ep)
                raw_lang = lang
                if use_key_raw:
                    add_attributes_to_hdf5(ep_p, raw_lang, encoded_lang)

                else:
                    add_sub_reason_distilbert_h5py(ep_p, raw_lang, encoded_lang, tokenizer)
        


