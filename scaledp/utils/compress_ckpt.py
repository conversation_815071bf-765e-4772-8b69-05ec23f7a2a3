import torch
import argparse

# add a command line argument for the model path
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt_dir', required=True)
    parser.add_argument('--nets', action='store', type=int, help='nets', required=True)
    args = parser.parse_args()
    ckpt_dir = args.ckpt_dir
    with_nets = args.nets > 0
    # Load the model from the checkpoint
    model = torch.load(ckpt_dir,map_location=torch.device('cpu'))
    parent_dir = '/'.join(ckpt_dir.split('/')[:-1])
    ckpt_name = ckpt_dir.split('/')[-1][:-5]
    x = {"ema": model['nets']['ema']}
    if with_nets:
        x["nets"] = model['nets']['nets']
    print(f'{parent_dir}/{ckpt_name}_compressed.ckpt')
    torch.save(x, f'{parent_dir}/{ckpt_name}_compressed.ckpt')