
import torch.nn as nn
import torch
import math
class FiLM(nn.Module):
    def __init__(self, feature_dim, condition_dim):
        super(FiLM, self).__init__()
        self.scale_fc = nn.Linear(condition_dim, feature_dim)
        self.shift_fc = nn.Linear(condition_dim, feature_dim)

        nn.init.zeros_(self.scale_fc.weight)
        nn.init.zeros_(self.scale_fc.bias)
        nn.init.zeros_(self.shift_fc.weight)
        nn.init.zeros_(self.shift_fc.bias)

    def forward(self, x, condition):
        # 计算缩放和偏移参数
        scale = self.scale_fc(condition)
        shift = self.shift_fc(condition)

        # 应用 FiLM 调制
        return x * (1 + scale) + shift