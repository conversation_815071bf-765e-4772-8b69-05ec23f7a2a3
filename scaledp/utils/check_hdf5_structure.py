import h5py


def print_hdf5_structure(hdf5_path):
    """
    Print the structure of an HDF5 file

    Args:
        hdf5_path (str): Path to the HDF5 file
    """
    try:
        with h5py.File(hdf5_path, 'r') as f:
            def print_attrs(name, obj):
                print(f"\nDataset/Group: {name}")
                print(f"Type: {type(obj)}")
                if isinstance(obj, h5py.Dataset):
                    print(f"Shape: {obj.shape}")
                    print(f"Data type: {obj.dtype}")
                print("Attributes:")
                for key, value in obj.attrs.items():
                    print(f"  {key}: {value}")
                print("-" * 50)

            print(f"\nStructure of HDF5 file: {hdf5_path}")
            print("=" * 50)
            print(f['substep_reasonings'].shape)
            print(f['sub_reason_distilbert'].shape)
            print(len(f['action']))
            print(f"Number of sub-items in the group: {len(list(f['action'].keys()))}")
            f.visititems(print_attrs)

    except Exception as e:
        print(f"Error reading HDF5 file: {str(e)}")

if __name__ == "__main__":
    # "/data/private/joy/data_test/task/collection_1748586445.hdf5"
    # hdf5_file=/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250530_random_fold_stacked_T-shirts_zby_compressed/collection_1748586445.hdf5
    print_hdf5_structure("/data/private/joy/data_test/task/collection_1748586445.hdf5")
