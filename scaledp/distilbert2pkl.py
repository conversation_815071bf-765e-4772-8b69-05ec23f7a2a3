import pickle


import json
import torch
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModel
tokenizer = AutoTokenizer.from_pretrained('/data/team/ljm/distilbert-base-uncased')
model = AutoModel.from_pretrained("/data/team/ljm/distilbert-base-uncased", torch_dtype=torch.float16)
model.to('cuda')

task_name="3_cameras_text"
with open("{}.json".format(f"{task_name}"), "r") as f:
    sub_dict = json.load(f)
lan_dict = dict()
for sub_dict in tqdm(sub_dict.values()):
    if type(sub_dict) == list:
        for sub_reason in sub_dict:
            if sub_reason not in lan_dict.keys():
                encoded_input = tokenizer(sub_reason, return_tensors='pt').to('cuda')
                outputs = model(**encoded_input)
                encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
                lan_dict[sub_reason] = encoded_lang.cpu().detach().numpy()
    else:
        encoded_input = tokenizer(sub_dict, return_tensors='pt').to('cuda')
        outputs = model(**encoded_input)
        encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
        lan_dict[sub_dict] = encoded_lang.cpu().detach().numpy()

with open("{}.pkl".format(f"{task_name}"), "wb") as f:
    pickle.dump(lan_dict, f)