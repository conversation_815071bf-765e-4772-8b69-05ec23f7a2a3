# π0风格DiT架构修改说明

## 修改概述

本文档详细说明了如何将ScaledP的DiT模型从AdaLN条件化架构修改为π0风格的joint attention架构。

## 主要修改内容

### 1. 移除AdaLN相关组件

#### 原始AdaLN架构
```python
# 原始DiTBlock
class DiTBlockRope(nn.Module):
    def __init__(self, ...):
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(),
            nn.Linear(hidden_size, 6 * hidden_size, bias=True)
        )
    
    def forward(self, x, c, pos, attn_mask=None):
        shift_msa, scale_msa, gate_msa, shift_mlp, scale_mlp, gate_mlp = \
            self.adaLN_modulation(c).chunk(6, dim=1)
        x = x + gate_msa.unsqueeze(1) * self.attn(
            modulate(self.norm1(x), shift_msa, scale_msa), pos, attn_mask=attn_mask
        )
        x = x + gate_mlp.unsqueeze(1) * self.mlp(
            modulate(self.norm2(x), shift_mlp, scale_mlp)
        )
        return x
```

#### 修改后的标准架构
```python
# π0风格DiTBlock
class DiTBlockRope(nn.Module):
    def __init__(self, ...):
        # 移除adaLN_modulation
        pass
    
    def forward(self, x, pos, attn_mask=None):
        # 标准transformer block，无AdaLN条件化
        x = x + self.attn(self.norm1(x), pos, attn_mask=attn_mask)
        x = x + self.mlp(self.norm2(x))
        return x
```

### 2. 时间嵌入方式改变

#### 原始时间嵌入
```python
# MLP-based timestep embedding
self.t_embedder = TimestepEmbedder(n_emb)
t = self.t_embedder(t)  # (N, D)
c = t + global_cond     # 全局条件
```

#### π0风格时间嵌入
```python
# Sinusoidal positional embedding
def create_sinusoidal_pos_embedding(time, dimension, min_period, max_period, device):
    # 正弦余弦位置编码
    half_dim = dimension // 2
    emb = torch.log(torch.tensor(max_period / min_period)) / (half_dim - 1)
    emb = torch.exp(torch.arange(half_dim) * -emb)
    emb = time[:, None] * emb[None, :]
    return torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)

# 时间与动作融合
time_emb = create_sinusoidal_pos_embedding(t, self.n_emb, 4e-3, 4.0, device)
action_emb = self.action_in_proj(x)
time_emb = time_emb[:, None, :].expand_as(action_emb)
action_time_emb = torch.cat([action_emb, time_emb], dim=2)
action_time_emb = self.action_time_mlp_out(F.silu(self.action_time_mlp_in(action_time_emb)))
```

### 3. Joint Attention机制

#### 原始注意力
```python
# 标准自注意力，通常使用因果掩码
attn_mask = causal_mask  # 简单的因果掩码
x = self.attn(x, pos, attn_mask=attn_mask)
```

#### π0风格Joint Attention
```python
def make_joint_attention_mask(cond_len, action_len, device, dtype):
    """
    创建复杂的联合注意力掩码：
    - 条件token可以关注所有条件token
    - 动作token可以关注所有条件token和之前的动作token
    """
    total_len = cond_len + action_len
    mask = torch.zeros(total_len, total_len)
    
    # 条件token之间的注意力
    mask[:cond_len, :cond_len] = 1
    
    # 动作token关注条件token
    mask[cond_len:, :cond_len] = 1
    
    # 动作token的因果注意力
    for i in range(action_len):
        mask[cond_len + i, cond_len:cond_len + i + 1] = 1
    
    return mask.masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, 0.0)
```

### 4. 模型初始化修改

#### 原始初始化
```python
# AdaLN相关初始化
for block in self.blocks:
    nn.init.constant_(block.adaLN_modulation[-1].weight, 0)
    nn.init.constant_(block.adaLN_modulation[-1].bias, 0)
```

#### π0风格初始化
```python
# 时间-动作融合MLP初始化
nn.init.normal_(self.action_time_mlp_in.weight, std=0.02)
nn.init.constant_(self.action_time_mlp_in.bias, 0)
nn.init.normal_(self.action_time_mlp_out.weight, std=0.02)
nn.init.constant_(self.action_time_mlp_out.bias, 0)
```

## 修改的优势

### 1. **更直接的时间条件化**
- π0方式：时间信息直接融合到token中，更自然
- 原始方式：通过AdaLN间接调制，可能丢失时间信息

### 2. **更灵活的注意力机制**
- π0方式：可以精确控制不同模态间的注意力模式
- 原始方式：全局条件化，缺乏细粒度控制

### 3. **更好的多模态融合**
- π0方式：通过attention mask实现复杂的模态交互
- 原始方式：主要依赖全局条件，融合能力有限

### 4. **更符合Flow Matching**
- π0方式：连续时间嵌入更适合Flow Matching的连续性质
- 原始方式：离散时间步嵌入，可能不够平滑

## 使用方法

```python
# 创建π0风格的DiT模型
model = DiT(
    input_dim=14,      # 动作维度
    output_dim=14,     # 输出维度
    cond_dim=512,      # 条件特征维度
    horizon=16,        # 动作序列长度
    depth=12,          # transformer层数
    n_emb=512,         # 嵌入维度
    head_dim=64,       # 注意力头维度
    num_heads=8        # 注意力头数
)

# 前向传播
output = model(
    x=noisy_actions,           # (B, T, action_dim)
    t=timesteps,               # (B,) 时间步
    global_cond=conditions,    # (B, cond_len, cond_dim)
    position_ids=pos_ids,      # (B, total_len)
    attn_mask=joint_mask       # (total_len, total_len)
)
```

## 注意事项

1. **内存使用**：Joint attention mask会增加内存使用，特别是长序列
2. **计算复杂度**：复杂的attention mask可能增加计算开销
3. **训练稳定性**：需要仔细调整学习率和初始化策略
4. **兼容性**：需要修改相关的训练和推理代码

## 总结

这种修改将ScaledP从基于AdaLN的全局条件化架构转换为π0风格的token级条件化架构，提供了更灵活的多模态融合能力和更直接的时间条件化方式。虽然增加了一些复杂性，但在理论上应该能提供更好的性能，特别是在复杂的多模态机器人控制任务中。
