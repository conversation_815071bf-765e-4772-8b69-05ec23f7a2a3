import h5py
import os

from aloha_scripts.constants import TASK_CONFIGS
import pickle
import json
import torch
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModel
tokenizer = AutoTokenizer.from_pretrained('/data/team/ljm/distilbert-base-uncased')
model = AutoModel.from_pretrained("/data/team/ljm/distilbert-base-uncased", torch_dtype=torch.float16)
model.to('cuda')


task_name="3_cameras_text"


task_path = TASK_CONFIGS[task_name]['dataset_dir']
os.makedirs("./task_info", exist_ok=True)
sub_dict = dict()
for task_name in task_path:
    print(task_name)
    h5_list = os.listdir(os.path.join(task_name))
    for h5_name in h5_list:
        if h5_name.endswith(".hdf5") and not h5_name.startswith("."):
            with h5py.File(os.path.join(task_name, h5_name), "r") as f:
                if "reasoning" in f.keys():
                    sub_dict[os.path.join(task_name, h5_name)] = f["reasoning"][:][0].decode("utf-8")
                if "shorts" in task_name:
                    sub_reason = f["substep_reasonings"][:].tolist()
                    sub_reason = [i.decode("utf-8") for i in sub_reason]
                    sub_dict[os.path.join(task_name, h5_name)] = sub_reason
    with open(os.path.join(task_name, "reason.json"), "w") as f:
        json.dump(sub_dict, f)

for task_name in task_path:
    with open(os.path.join(task_name, "reason.json"), "r") as f:
        sub_dict = json.load(f)
    lan_dict = dict()
    for sub_dict in tqdm(sub_dict.values()):
        if type(sub_dict) == list:
            for sub_reason in sub_dict:
                if sub_reason not in lan_dict.keys():
                    encoded_input = tokenizer(sub_reason, return_tensors='pt').to('cuda')
                    outputs = model(**encoded_input)
                    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
                    lan_dict[sub_reason] = encoded_lang.cpu().detach().numpy()
        else:
            encoded_input = tokenizer(sub_dict, return_tensors='pt').to('cuda')
            outputs = model(**encoded_input)
            encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
            lan_dict[sub_dict] = encoded_lang.cpu().detach().numpy()

    with open(os.path.join(task_name, "reason.pkl"), "wb") as f:
        pickle.dump(lan_dict, f)