### Task parameters


DATA_DIR = '/data/team/wk/datasets/real_franka/act_datasets'
DATA_DIR="/home/<USER>/tzb/wjj/data"

print("")
TASK_CONFIGS = {
    'folding_data_0609': {
        'dataset_dir': [
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250530_random_fold_stacked_T-shirts_zby_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_2_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250521_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250522_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250523_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_zby_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250528_fold_T-shirts_zby_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_lyp_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250526_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250527_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250530_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_zjm_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_Leo_20250522_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250522_compressed",
            # "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250523_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_Leo_20250526_noon_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_compressed"
        ],
        'episode_len': 1000,
        'camera_names': ['cam_high', 'cam_left_wrist', 'cam_right_wrist']
    },
    'add_data': {
        'dataset_dir': [
            '/media/eai/MAD-2/aloha_data/fold_t_shirt_easy_version',

        ],
    },

    "screwing_6_7":{
        'dataset_dir': [
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_1col_06_05_lyp_480_640/1row_1col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_2col_06_05_lyp_480_640/1row_2col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_3col_06_05_lyp_480_640/1row_3col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_4col_06_05_lyp_480_640/1row_4col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/2row_1col_06_05_lyp_480_640/2row_1col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_1col_06_06_lyp_480_640/1row_1col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_2col_06_06_lyp_480_640/1row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_3col_06_06_lyp_480_640/1row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_2col_06_06_lyp_480_640/2row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_3col_06_06_lyp_480_640/2row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_4col_06_06_lyp_480_640/2row_4col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_1col_06_06_lyp_480_640/3row_1col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_2col_06_06_lyp_480_640/3row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_3col_06_06_lyp_480_640/3row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_4col_06_06_lyp_480_640/3row_4col_06_06_lyp_480_640_succ_t0001_s-0-0",
        ],
        'episode_len': 1000,  
        'camera_names': ['left', 'front', 'wrist'] # replacing with your real keys in h5py formatted data\
    },
    
"screwing_6_7_2_views":{
        'dataset_dir': [
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_1col_06_05_lyp_480_640/1row_1col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_2col_06_05_lyp_480_640/1row_2col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_3col_06_05_lyp_480_640/1row_3col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/1row_4col_06_05_lyp_480_640/1row_4col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250605/2row_1col_06_05_lyp_480_640/2row_1col_06_05_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_1col_06_06_lyp_480_640/1row_1col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_2col_06_06_lyp_480_640/1row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/1row_3col_06_06_lyp_480_640/1row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_2col_06_06_lyp_480_640/2row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_3col_06_06_lyp_480_640/2row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/2row_4col_06_06_lyp_480_640/2row_4col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_1col_06_06_lyp_480_640/3row_1col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_2col_06_06_lyp_480_640/3row_2col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_3col_06_06_lyp_480_640/3row_3col_06_06_lyp_480_640_succ_t0001_s-0-0",
            "/data/team/eai-data/screwing_0605_0606/screwing_lyp_20250606/3row_4col_06_06_lyp_480_640/3row_4col_06_06_lyp_480_640_succ_t0001_s-0-0",
        ],
        'episode_len': 1000,  
        'camera_names': ['left',  'wrist'] # replacing with your real keys in h5py formatted data\
    },
'3_cameras_text': {
        'dataset_dir': [
            '/mnt/HDD/robot_data/h5_py_data_compressed_70/2025-05-02_tighten_screws_480_640/2025-05-02_tighten_screws_480_640_succ_t0001_s-0-0/',
        ],
        'episode_len': 1000,  # 1000,
        # 'camera_names': ['cam_high', 'cam_low', 'cam_left_wrist', 'cam_right_wrist']
        'camera_names':['left', "front",'right'],
    },




}

### ALOHA fixed constants
DT = 0.02
JOINT_NAMES = ["waist", "shoulder", "elbow", "forearm_roll", "wrist_angle", "wrist_rotate"]
START_ARM_POSE = [0, -0.96, 1.16, 0, -0.3, 0, 0.02239, -0.02239,  0, -0.96, 1.16, 0, -0.3, 0, 0.02239, -0.02239]

# Left finger position limits (qpos[7]), right_finger = -1 * left_finger
MASTER_GRIPPER_POSITION_OPEN = 0.02417
MASTER_GRIPPER_POSITION_CLOSE = 0.01244
PUPPET_GRIPPER_POSITION_OPEN = 0.05800
PUPPET_GRIPPER_POSITION_CLOSE = 0.01844

# Gripper joint limits (qpos[6])
MASTER_GRIPPER_JOINT_OPEN = 0.3083
MASTER_GRIPPER_JOINT_CLOSE = -0.6842
PUPPET_GRIPPER_JOINT_OPEN = 1.4910
PUPPET_GRIPPER_JOINT_CLOSE = -0.6213

############################ Helper functions ############################

MASTER_GRIPPER_POSITION_NORMALIZE_FN = lambda x: (x - MASTER_GRIPPER_POSITION_CLOSE) / (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE)
PUPPET_GRIPPER_POSITION_NORMALIZE_FN = lambda x: (x - PUPPET_GRIPPER_POSITION_CLOSE) / (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE)
MASTER_GRIPPER_POSITION_UNNORMALIZE_FN = lambda x: x * (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE) + MASTER_GRIPPER_POSITION_CLOSE
PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN = lambda x: x * (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE) + PUPPET_GRIPPER_POSITION_CLOSE
MASTER2PUPPET_POSITION_FN = lambda x: PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN(MASTER_GRIPPER_POSITION_NORMALIZE_FN(x))

MASTER_GRIPPER_JOINT_NORMALIZE_FN = lambda x: (x - MASTER_GRIPPER_JOINT_CLOSE) / (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE)
PUPPET_GRIPPER_JOINT_NORMALIZE_FN = lambda x: (x - PUPPET_GRIPPER_JOINT_CLOSE) / (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE)
MASTER_GRIPPER_JOINT_UNNORMALIZE_FN = lambda x: x * (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE) + MASTER_GRIPPER_JOINT_CLOSE
PUPPET_GRIPPER_JOINT_UNNORMALIZE_FN = lambda x: x * (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE) + PUPPET_GRIPPER_JOINT_CLOSE
MASTER2PUPPET_JOINT_FN = lambda x: PUPPET_GRIPPER_JOINT_UNNORMALIZE_FN(MASTER_GRIPPER_JOINT_NORMALIZE_FN(x))

MASTER_GRIPPER_VELOCITY_NORMALIZE_FN = lambda x: x / (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE)
PUPPET_GRIPPER_VELOCITY_NORMALIZE_FN = lambda x: x / (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE)

MASTER_POS2JOINT = lambda x: MASTER_GRIPPER_POSITION_NORMALIZE_FN(x) * (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE) + MASTER_GRIPPER_JOINT_CLOSE
MASTER_JOINT2POS = lambda x: MASTER_GRIPPER_POSITION_UNNORMALIZE_FN((x - MASTER_GRIPPER_JOINT_CLOSE) / (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE))
PUPPET_POS2JOINT = lambda x: PUPPET_GRIPPER_POSITION_NORMALIZE_FN(x) * (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE) + PUPPET_GRIPPER_JOINT_CLOSE
PUPPET_JOINT2POS = lambda x: PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN((x - PUPPET_GRIPPER_JOINT_CLOSE) / (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE))

MASTER_GRIPPER_JOINT_MID = (MASTER_GRIPPER_JOINT_OPEN + MASTER_GRIPPER_JOINT_CLOSE)/2
