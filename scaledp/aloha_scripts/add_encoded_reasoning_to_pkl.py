import pickle
import os
import json
import torch
from transformers import AutoTokenizer, AutoModel

tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/')
model = AutoModel.from_pretrained("/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/",
                                  torch_dtype=torch.float16)
model.to('cuda')

def load_jsonl(p):
    data = []
    with open(p, "r") as f:
        for line in f:
            data.append(json.loads(line))
    return data

def encode_lang(lang):
    encoded_input = tokenizer(lang, return_tensors='pt').to('cuda')
    outputs = model(**encoded_input)
    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0).cpu().detach().numpy()
    return encoded_lang

task_list = [
    # "fold_two_shirts_zmj_03_26_lerobot",
    # "fold_two_shirts_zmj_03_21_lerobot",
    # "fold_two_shirts_wjj_03_21",
    # "fold_two_shirts_zmj_03_24_lerobot"
    "folding_random_tshirt_first_wjj_0124",
    "folding_random_tshirt_second_wjj_0124"
]
ROOT_DIR = "/home/<USER>/tzb/lerobot_data/aloha/"

for t in task_list:
    p = os.path.join(ROOT_DIR, t, "meta", "episodes.jsonl")
    save_file_name = os.path.join(ROOT_DIR, t, "distilbert.pkl")
    data = load_jsonl(p)
    reasoning_data = set()
    for episode in data:
        for sub_reason in episode["language_dict"]["substep_reasonings"]:
            reasoning_data.add(sub_reason)

    try:
        with open(save_file_name, "rb") as f:
            distillbert_dict = pickle.load(f)
    except:
        distillbert_dict = {}
    for sub_reason in reasoning_data:
        distillbert_dict[sub_reason] = encode_lang(sub_reason)

    with open(save_file_name, "wb") as f:
        pickle.dump(distillbert_dict, f)