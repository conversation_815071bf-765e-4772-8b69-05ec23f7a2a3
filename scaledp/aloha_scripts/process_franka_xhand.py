import argparse
import fnmatch
import h5py
import os
import numpy as np
from constants import *

def flatten_list(l):
    return [item for sublist in l for item in sublist]

def find_all_hdf5(dataset_dir):
    hdf5_files = []
    for root, dirs, files in os.walk(dataset_dir):
        for filename in fnmatch.filter(files, '*.hdf5'):
            if 'features' in filename:
                continue
            if 'pointclouds' in filename:
                continue
            hdf5_files.append(os.path.join(root, filename))
    print(f'Found {len(hdf5_files)} hdf5 files')
    assert len(hdf5_files) > 0
    return hdf5_files

def change_h5_data(args):
    task_config = TASK_CONFIGS[args.task_name]
    dataset_dir_l = task_config['dataset_dir']
    dataset_path_list_list = [find_all_hdf5(dataset_dir) for dataset_dir in dataset_dir_l]
    dataset_path_list = flatten_list(dataset_path_list_list)

    for h5py_file_name in dataset_path_list:
        if not h5py_file_name.endswith('.hdf5'):
            continue
        with h5py.File(h5py_file_name,"a") as f:
            ori_action = f["action"][:]
            qpos_data = f['/observations/qpos'][:]
            if 'action_ori' in f:
                del f['action_ori']
            if '/observations/qpos_ori' in f:
                del f['/observations/qpos_ori']
            f.create_dataset('action_ori', data=ori_action)
            f.create_dataset('/observations/qpos_ori', data=qpos_data)
            action = np.hstack((ori_action[:,0:9],ori_action[:,13:14]))
            state = np.hstack((qpos_data[:,0:6],qpos_data[:,10:11]))
            del f['action']
            del f['/observations/qpos']
            f.create_dataset('action', data=action)
            f.create_dataset('/observations/qpos', data=state)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    ### training hyperparameters
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)

    args = parser.parse_args()
    change_h5_data(args)