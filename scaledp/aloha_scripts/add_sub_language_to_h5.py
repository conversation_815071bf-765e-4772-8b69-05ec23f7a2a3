import os.path
import h5py
import os
import torch
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModel

tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/')
model = AutoModel.from_pretrained("/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/",
                                  torch_dtype=torch.float16)
model.to('cuda')

def add_language_to_hdf5(file_path):
    with h5py.File(file_path, 'a') as hdf5_file:
        lang_raw_list = hdf5_file["language_raw"][()]
        sub_language_distilbert_list = []

        for l in lang_raw_list:
            sub_language_distilbert_list.append(encode_language_with_distillbert(l.decode('utf-8')))
        if "sub_language_distilbert" not in hdf5_file.keys():
            hdf5_file.create_dataset("sub_language_distilbert", data=sub_language_distilbert_list)

def encode_language_with_distillbert(raw_lang):
    encoded_input = tokenizer(raw_lang, return_tensors="pt").to("cuda")
    outputs = model(**encoded_input)
    encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)
    return encoded_lang.cpu().detach().numpy()

if __name__=="__main__":
    tasks = [
        "white_soup_spoon_green_bowl_orange_paper_cup_nescafe_coffee",
        "blue_cup_beige_bowl_Costa_coffee_green_paper_cup",
    ]
    DATA_DIR = '/home/<USER>/tzb/h5py_data/aloha_quest3_data/cyf_clean_table/compressed/'
    for task in tasks:
        file_path = os.path.join(DATA_DIR, task)
        file_list = os.listdir(file_path)
        file_list = [f for f in file_list if f.endswith('.hdf5')]

        for file_name in tqdm(file_list):
            print(file_path, file_name)
            add_language_to_hdf5(file_path=os.path.join(file_path, file_name))