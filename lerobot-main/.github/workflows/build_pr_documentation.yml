name: Build PR Documentation

on:
  pull_request:
    paths:
      - "docs/**"

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build:  # zizmor: ignore[excessive-permissions] We follow the same pattern as in Transformers
    uses: huggingface/doc-builder/.github/workflows/build_pr_documentation.yml@main
    with:
      commit_sha: ${{ github.event.pull_request.head.sha }}
      pr_number: ${{ github.event.number }}
      package: lerobot
      additional_args: --not_python_module
