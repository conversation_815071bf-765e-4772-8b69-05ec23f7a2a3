# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from lerobot.common.transport import services_pb2 as lerobot_dot_common_dot_transport_dot_services__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in lerobot/common/transport/services_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LearnerServiceStub:
    """LearnerService: the Actor calls this to push transitions.
    The Learner implements this service.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StreamParameters = channel.unary_stream(
                '/transport.LearnerService/StreamParameters',
                request_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
                response_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Parameters.FromString,
                _registered_method=True)
        self.SendTransitions = channel.stream_unary(
                '/transport.LearnerService/SendTransitions',
                request_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Transition.SerializeToString,
                response_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
                _registered_method=True)
        self.SendInteractions = channel.stream_unary(
                '/transport.LearnerService/SendInteractions',
                request_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.InteractionMessage.SerializeToString,
                response_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
                _registered_method=True)
        self.Ready = channel.unary_unary(
                '/transport.LearnerService/Ready',
                request_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
                response_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
                _registered_method=True)


class LearnerServiceServicer:
    """LearnerService: the Actor calls this to push transitions.
    The Learner implements this service.
    """

    def StreamParameters(self, request, context):
        """Actor -> Learner to store transitions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendTransitions(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendInteractions(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Ready(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LearnerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StreamParameters': grpc.unary_stream_rpc_method_handler(
                    servicer.StreamParameters,
                    request_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
                    response_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Parameters.SerializeToString,
            ),
            'SendTransitions': grpc.stream_unary_rpc_method_handler(
                    servicer.SendTransitions,
                    request_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Transition.FromString,
                    response_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
            ),
            'SendInteractions': grpc.stream_unary_rpc_method_handler(
                    servicer.SendInteractions,
                    request_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.InteractionMessage.FromString,
                    response_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
            ),
            'Ready': grpc.unary_unary_rpc_method_handler(
                    servicer.Ready,
                    request_deserializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
                    response_serializer=lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'transport.LearnerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('transport.LearnerService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LearnerService:
    """LearnerService: the Actor calls this to push transitions.
    The Learner implements this service.
    """

    @staticmethod
    def StreamParameters(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/transport.LearnerService/StreamParameters',
            lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
            lerobot_dot_common_dot_transport_dot_services__pb2.Parameters.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SendTransitions(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            '/transport.LearnerService/SendTransitions',
            lerobot_dot_common_dot_transport_dot_services__pb2.Transition.SerializeToString,
            lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SendInteractions(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            '/transport.LearnerService/SendInteractions',
            lerobot_dot_common_dot_transport_dot_services__pb2.InteractionMessage.SerializeToString,
            lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Ready(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/transport.LearnerService/Ready',
            lerobot_dot_common_dot_transport_dot_services__pb2.Empty.SerializeToString,
            lerobot_dot_common_dot_transport_dot_services__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
